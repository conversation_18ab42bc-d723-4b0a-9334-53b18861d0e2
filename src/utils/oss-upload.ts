// oss-upload.ts
// 通用OSS上传工具，实际实现需对接后端/三方SDK

import * as OssTypes from '@/types/ai-material/oss';
import { gdRequest } from '@/services/request';

import { uuid } from '@alife/kb-biz-util';
import { ClientSts } from './oss-client';
import { getEnv } from '@/common/utils';
import { Env } from '@/common/const';

const ossConfig = {
  bucketName: 'amap-sales-operation-prod',
  ossConfigId: '4071ecfb4ada4cd7',
};

// 获取OSS签名
export const getOssSign = async (): Promise<OssTypes.IGetOssSignResult> => {
  return gdRequest('mtop.amap.mp.merchant.oss.getPolicySignOuter', {
    accountID: '',
    service: 'amap-c3-oss',
    func: 'policyHsfService-getPolicySignMse',
    event: {
      ...ossConfig,
    },
    qualifier: 'policyHsfService-getPolicySignMse__stable',
  });
};

// 获取图片访问url
export const getOssUrl = async (params: { fileKey: string }): Promise<string> => {
  return gdRequest('mtop.amap.mp.merchant.oss.getPolicyUrlOuter', {
    accountID: '',
    service: 'amap-c3-oss',
    func: 'policyHsfService-getPolicyUrlMse',
    event: {
      ...ossConfig,
      filePath: params.fileKey,
      generateUrlType: 'gd',
      process: '',
    },
    qualifier: 'policyHsfService-getPolicyUrlMse__stable',
  });
};

// 主上传方法，返回最终图片url
export interface UploadResourceOptions {
  file: File;
  contentType?: string;
  needDomainChange?: boolean;
  isOriginalPic?: boolean;
}

const ossUpload = async (file: File): Promise<{ url: string; fileKey: string }> => {
  // 1. 获取签名
  const ossSign = await getOssSign();
  if (!ossSign) throw new Error('获取OSS签名失败');
  const ext = file.name.split('.').pop();

  // 2. 上传文件到OSS
  const id = uuid();
  const env = getEnv() === Env.Prod ? 'PROD' : 'PRE';
  const key = `${env}/media/out/${id}.${ext}`;
  const clinet = new ClientSts({
    policy: ossSign,
    bucketName: ossConfig.bucketName,
    getPolicySign: getOssSign,
  });
  await clinet.uploadOSS(file, key);

  // 3. 获取图片访问url
  const ossUrlResult = await getOssUrl({
    fileKey: key,
  });
  if (!ossUrlResult) throw new Error('获取图片URL失败');

  return {
    url: ossUrlResult,
    fileKey: key,
  };
};

// 专门用于任务文件上传的函数
export const uploadTaskFile = async (file: File): Promise<{ url: string; fileKey: string }> => {
  // 1. 获取签名
  const ossSign = await getOssSign();
  if (!ossSign) throw new Error('获取OSS签名失败');
  const ext = file.name.split('.').pop();

  // 2. 上传文件到OSS
  const id = uuid();
  const env = getEnv() === Env.Prod ? 'PROD' : 'PRE';
  const key = `${env}/task/${id}.${ext}`;
  const clinet = new ClientSts({
    policy: ossSign,
    bucketName: ossConfig.bucketName,
    getPolicySign: getOssSign,
  });
  await clinet.uploadOSS(file, key);

  // 3. 获取图片访问url
  const ossUrlResult = await getOssUrl({
    fileKey: key,
  });
  if (!ossUrlResult) throw new Error('获取图片URL失败');

  return {
    url: ossUrlResult,
    fileKey: key,
  };
};

export default ossUpload;
