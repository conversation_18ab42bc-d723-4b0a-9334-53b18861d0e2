import React from 'react';
import { Flex } from 'antd';
import StoreDashboard from './components/StoreDashboard';
import MerchantStratification from './components/MerchantStratification';
import TaskDetails from './components/TaskDetails';
import FilterBar from './components/FilterBar';
import Performance from './components/Performance';
import { FilterProvider, useFilterOptions } from '@/context/FilterContext';

const ManagerBattleMapContent: React.FC = () => {
  const { setFilterOptions } = useFilterOptions();

  return (
    <Flex gap={16} style={{ padding: 16, background: '#F5F5F5' }} vertical>
      {/* 筛选条件栏 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div style={{ fontSize: 16, fontWeight: 500 }}>管理者作战地图</div>
        <FilterBar onChange={setFilterOptions} />
      </div>

      {/* 绩效 */}
      <Performance />

      {/* 门店仪表盘 */}
      <StoreDashboard />

      {/* 商户分层 */}
      <MerchantStratification />

      {/* 任务明细 */}
      <TaskDetails />
    </Flex>
  );
};

function ManagerBattleMap() {
  return (
    <FilterProvider>
      <ManagerBattleMapContent />
    </FilterProvider>
  );
}

export default ManagerBattleMap;
