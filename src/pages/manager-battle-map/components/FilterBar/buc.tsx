import React, { useEffect, useCallback, useState } from 'react';
import { Select, Form } from 'antd';
import { useRequest } from 'ahooks';
import afetch from '@alife/amap-fetch';
import JobTree from '@alife/mo-job-tree-select';
import type { FilterOptions, UserInfo } from '../../types';
import { getEnv, Env } from '@alife/amap-mp-utils';

// 预发产技测试临时 30234568
// 线上渠道商家成长部 560102013
const CHANNEL_BUSINESS_DEPT_ID = getEnv() === Env.PRE ? '30234568' : '560102013';

/**
 * 组件Props接口
 */
interface BucFilterProps {
  /** 筛选条件变化回调 */
  onFilterChange: (filterOptions: FilterOptions) => void;
  /** 初始值 */
  defaultValue?: Partial<FilterOptions>;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * BUC筛选组件 - 用于内部员工
 *
 * ## 功能说明
 *
 * ### 主要功能
 * 1. **组织架构选择**: 使用 @alife/mo-job-tree-select 组件进行组织架构选择
 * 2. **条件联动**: 根据组织架构选择结果动态显示后续筛选项
 * 3. **渠道业务部判断**: 检查选中节点路径是否包含"渠道业务部"
 * 4. **用户筛选**: 展示组织架构选择结果的extra.userId列表
 * 5. **服务商查询**: 调用searchAgent接口获取服务商列表
 * 6. **筛选联动**: 最终触发全局filterOptions变化
 *
 * ### 交互逻辑
 * ```
 * 组织架构选择 → 判断路径是否包含"渠道业务部"
 *                    ↓
 *                包含 → 显示用户选择框 → 显示服务商选择框
 *                    ↓                    ↓
 *               选择用户 → 调用searchAgent → 选择服务商 → 触发filterOptions
 *                    ↓
 *               不包含 → 直接触发filterOptions
 * ```
 */
const BucFilter: React.FC<BucFilterProps> = ({
  onFilterChange,
  defaultValue,
  disabled = false,
}) => {
  const [form] = Form.useForm();

  // 使用 useWatch 监听表单值变化
  const organizationValue = Form.useWatch('organization', form);
  const userIdValue = Form.useWatch('userId', form);

  // 通过 useRef 计算是否显示选择器
  const showUserSelect = organizationValue?.node?.value?.includes(CHANNEL_BUSINESS_DEPT_ID);
  const showAgentSelect = showUserSelect && userIdValue;
  const [userOptions, setUserOptions] = useState<UserInfo[]>([]);

  /**
   * 服务商查询请求
   */
  const {
    data: agentOptions = [],
    loading: agentLoading,
    run: searchAgents,
  } = useRequest(
    async (userId: string) => {
      if (!userId) return [];

      try {
        // 调用searchAgent接口获取服务商列表
        const result = await afetch({
          params: {
            action: 'alsc-kbt-merchant-admin.AlscAgentGateWayWeb.queryAlscAgentCompanyByParams',
            bizContent: {
              operationOsUserId: userId,
            },
          },
        });

        // 转换数据格式为组件需要的格式
        return result?.data?.data?.result || [];
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('查询服务商列表失败:', error);
        return [];
      }
    },
    {
      manual: true,
      onError: (error) => {
        // eslint-disable-next-line no-console
        console.error('searchAgent接口调用失败:', error);
      },
    },
  );

  /**
   * 服务商选择变化处理
   * @param agentId 选中的服务商ID
   */
  const handleAgentChange = useCallback(() => {
    // 现在通过 useEffect 监听 agentIdValue 变化来处理
    // 这里不需要额外处理，Form会自动更新值并触发useWatch
  }, []);

  /**
   * 表单字段变化监听
   */
  const handleFieldsChange = useCallback(
    (changedFields: any[]) => {
      changedFields.forEach((field) => {
        const { name, value } = field;

        if (name[0] === 'organization') {
          form.setFieldsValue({ userId: undefined, agentId: undefined });
          // 检查value.value是否包含CHANNEL_BUSINESS_DEPT_ID
          if (value?.node?.value?.includes(CHANNEL_BUSINESS_DEPT_ID)) {
            // 包含渠道业务部ID，取value.node.members作为用户选择options
            const members = value.node?.members || [];
            const userList: UserInfo[] = members.map((member: any) => ({
              userId: member.userId || member.id,
              displayName: member.displayName,
            }));
            setUserOptions(userList || []);
          } else {
            // 不包含渠道业务部ID，清空用户选项并直接触发筛选
            setUserOptions([]);
            // 直接触发筛选，传递组织架构信息
            onFilterChange({
              deptId: value.value,
            });
          }
        } else if (name[0] === 'userId') {
          const userId = value;
          searchAgents(userId);
          form.setFieldsValue({
            agentId: undefined,
          });
        } else if (name[0] === 'agentId') {
          const formValues = form.getFieldsValue();
          onFilterChange({
            companyId: formValues.agentId,
            aliCode: formValues.userId,
            deptId: formValues.organization.value,
          });
        }
      });
    },
    [form, searchAgents, handleAgentChange, onFilterChange, setUserOptions],
  );

  /**
   * 初始化默认值
   */
  useEffect(() => {
    if (defaultValue) {
      form.setFieldsValue({
        organization: defaultValue.deptId ? { value: defaultValue.deptId } : undefined,
        userId: defaultValue.aliCode,
        agentId: defaultValue.companyId,
      });
    }
  }, [defaultValue, form]);

  return (
    <Form form={form} layout="inline" onFieldsChange={handleFieldsChange}>
      <div style={{ display: 'flex' }}>
        {/* 组织架构选择 - 使用 @alife/mo-job-tree-select */}
        <Form.Item name="organization">
          <JobTree style={{ width: 300 }} onlyUserJob onlyWorkJob multiple={false} />
        </Form.Item>

        {/* 用户选择框 - 仅在包含渠道业务部时显示 */}
        {showUserSelect && (
          <Form.Item name="userId">
            <Select
              style={{ width: 240 }}
              placeholder="选择小二"
              allowClear
              disabled={disabled}
              options={userOptions.map((user) => ({
                value: user.userId,
                label: user.displayName,
                title: `${user.displayName}(${user.userId})`,
              }))}
              showSearch
              filterOption={(input, option) =>
                (option?.label?.toString() || '').toLowerCase().includes(input.toLowerCase()) ||
                (option?.title?.toString() || '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
        )}

        {/* 服务商选择框 - 仅在选择用户后显示 */}
        {showAgentSelect && (
          <Form.Item name="agentId">
            <Select
              style={{ width: 240 }}
              placeholder="选择服务商"
              allowClear
              disabled={disabled}
              loading={agentLoading}
              options={agentOptions.map((agent) => ({
                value: agent.companyId,
                label: agent.companyName,
              }))}
              showSearch
              filterOption={(input, option) =>
                (option?.label?.toString() || '').toLowerCase().includes(input.toLowerCase())
              }
              notFoundContent={agentLoading ? '加载中...' : '暂无数据'}
            />
          </Form.Item>
        )}
      </div>
    </Form>
  );
};

export default BucFilter;
