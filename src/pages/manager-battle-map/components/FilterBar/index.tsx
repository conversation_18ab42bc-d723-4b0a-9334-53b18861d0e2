import React from 'react';
import BucFilter from './buc';
import AgentFilter from './agent';
import type { FilterOptions } from '../../types';

/**
 * 组件Props接口
 */
interface FilterBarProps {
  /** 筛选条件变化回调 */
  onChange: (filterOptions: FilterOptions) => void;
  /** 初始值 */
  defaultValue?: Partial<FilterOptions>;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 筛选条件组件 - 根据用户类型选择不同的筛选组件
 *
 * ## 功能说明
 *
 * ### 组件选择逻辑
 * - **BUC用户** (`window.APP.operatorType === 'BUC'`): 使用 BucFilter 组件
 *   - 完整的组织架构选择功能
 *   - 用户和服务商筛选功能
 *   - 支持复杂的筛选联动
 *
 * - **Agent用户** (其他类型): 使用 AgentFilter 组件
 *   - 只读显示当前服务商名称
 *   - 自动传递服务商ID作为筛选条件
 *   - 无法切换筛选条件
 *
 * ### 数据流
 * 1. 根据 `window.APP.operatorType` 判断用户类型
 * 2. 渲染对应的筛选组件
 * 3. 统一通过 `onChange` 回调传递筛选结果
 */
const FilterBar: React.FC<FilterBarProps> = ({ onChange, defaultValue, disabled = false }) => {
  // 获取用户类型
  const operatorType = (window as any)?.APP?.operatorType;

  // 根据用户类型选择对应的筛选组件
  if (operatorType === 'AMAP') {
    // 默认使用 Agent 筛选组件
    return (
      <AgentFilter onFilterChange={onChange} defaultValue={defaultValue} disabled={disabled} />
    );
  }

  // 默认使用 Agent 筛选组件
  return <BucFilter onFilterChange={onChange} defaultValue={defaultValue} disabled={disabled} />;
};

export default FilterBar;
