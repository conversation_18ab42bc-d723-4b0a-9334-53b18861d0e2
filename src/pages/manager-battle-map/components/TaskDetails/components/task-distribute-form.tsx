import React, { useState } from 'react';
import { Modal, Form, Input, Radio, InputNumber, Button, Space, message, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import styled from 'styled-components';
import { queryPriorityTaskType, uploadSupervisorTask } from '@/services';
import { uploadTaskFile } from '@/utils/oss-upload';

const { TextArea } = Input;

const FormContainer = styled.div`
  .ant-form-item {
    margin-bottom: 20px;
  }

  .ant-form-item-label > label {
    font-weight: 500;
  }
`;

const RadioGroup = styled(Radio.Group)`
  .ant-radio-wrapper {
    margin-right: 24px;
  }
`;

const UploadSection = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const TimeInputContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

interface ITaskDistributeFormProps {
  visible: boolean;
  onClose: () => void;
  scene?: 'LEADER_ISSUE';
}

const TaskDistributeForm: React.FC<ITaskDistributeFormProps> = ({
  visible,
  onClose,
  scene = 'LEADER_ISSUE',
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);

  // 获取任务类型列表
  const { data: taskTypes, loading: taskTypesLoading } = useRequest(
    () => queryPriorityTaskType({ scene: 'LEADER_ISSUE' }),
    {
      ready: visible,
    },
  );

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      if (fileList.length === 0) {
        message.error('请上传任务文件');
        return;
      }

      const file = fileList[0];

      // 先上传文件到 OSS
      message.loading('正在上传文件...', 0);
      const uploadResult = await uploadTaskFile(file.originFileObj || file);
      message.destroy();

      // 使用上传返回的 fileKey 作为路径
      const ossPath = uploadResult.fileKey;

      await uploadSupervisorTask({
        path: ossPath,
        taskType: values.taskType,
        taskName: values.taskName,
        taskDes: values.description,
        validDay: values.workingDays,
      });

      const messageText =
        scene === 'LEADER_ISSUE'
          ? `您主管给您下达了【${values.taskName}】，共包含【${fileList.length}】个商户，请及时作业`
          : `您有${fileList.length}个未完成的主管催办任务今日到期，请尽快处理`;

      message.success(messageText);
      form.resetFields();
      setFileList([]);
      onClose();
    } catch (error) {
      message.destroy();
      message.error('任务下发失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setFileList([]);
    onClose();
  };

  const handleFileChange = (info: any) => {
    const { file } = info;

    // 文件大小限制：1024KB
    if (file && file.size > 1024 * 1024) {
      message.error('文件大小不能超过1024KB');
      return;
    }

    setFileList(info.fileList);
  };
  // eslint-disable-next-line no-console
  console.log(taskTypes, 'taskTypes');
  return (
    <Modal
      title="任务下发"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={500}
      destroyOnClose
    >
      <FormContainer>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            workingDays: 5,
          }}
        >
          <Form.Item
            label="任务类型"
            name="taskType"
            rules={[{ required: true, message: '请选择任务类型' }]}
          >
            <RadioGroup>
              {(() => {
                if (taskTypesLoading) {
                  return <div style={{ color: '#999', padding: '8px 0' }}>加载中...</div>;
                }
                if (taskTypes?.typeList?.length > 0) {
                  return taskTypes.typeList.map((item) => (
                    <Radio key={item.taskType} value={item.taskType}>
                      {item.taskName}
                    </Radio>
                  ));
                }
                return <div style={{ color: '#999', padding: '8px 0' }}>暂无任务类型选项</div>;
              })()}
            </RadioGroup>
          </Form.Item>

          <Form.Item
            label="任务名称"
            name="taskName"
            rules={[
              { required: true, message: '请输入任务名称' },
              { max: 30, message: '任务名称最多30字' },
            ]}
          >
            <Input placeholder="请输入任务名称" maxLength={30} showCount />
          </Form.Item>

          <Form.Item
            label="任务描述"
            name="description"
            rules={[
              { required: true, message: '请输入任务描述' },
              { max: 100, message: '任务描述最多100字' },
            ]}
          >
            <TextArea rows={4} placeholder="请输入任务描述" maxLength={100} showCount />
          </Form.Item>

          <Form.Item
            label="时效要求"
            name="workingDays"
            rules={[
              { required: true, message: '请输入时效要求' },
              { min: 1, max: 30, message: '时效要求必须在1-30天之间' },
            ]}
            extra="规定时间内任务未完成则任务过期，最大30天"
          >
            <TimeInputContainer>
              <InputNumber min={1} max={30} precision={0} placeholder="1" />
              <span>工作日</span>
            </TimeInputContainer>
          </Form.Item>

          <Form.Item label="上传文档" required>
            <UploadSection>
              <Button
                type="link"
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = 'https://a.amap.com/smallBiz/static/xy-task/批量下发任务模板.xlsx';
                  link.download = '批量下发任务模板.xlsx';
                  link.style.display = 'none';
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}
              >
                下载模板
              </Button>
              <Upload
                fileList={fileList}
                onChange={handleFileChange}
                beforeUpload={() => false}
                accept=".xlsx,.xls,.csv"
              >
                <Button type="link" icon={<UploadOutlined />}>
                  上传文件
                </Button>
              </Upload>
            </UploadSection>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={handleCancel}>取消</Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </FormContainer>
    </Modal>
  );
};

export default TaskDistributeForm;
