_**原语雀文档链接：**_[https://yuque.antfin.com/william.gc/ieofpu/lytesc5owwnt8vve](https://yuque.antfin.com/william.gc/ieofpu/lytesc5owwnt8vve)

---

## 1. 需求背景

*   相关材料：[《8.8汇报版商家成长端到端任务对焦》](https://alidocs.dingtalk.com/i/nodes/m9bN7RYPWdyrPBREc3a7aqB7VZd1wyK0?cid=421069%3A592264883&utm_source=im&utm_scene=person_space&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&utm_medium=im_card&corpId=dingd8e1123006514592)
    
*   PRD：[《【prd】商家成长端到端任务2.0需求》](https://alidocs.dingtalk.com/i/nodes/MyQA2dXW7zyZ9R0mID4Njkzx8zlwrZgb?cid=592264883%3A4496711494&utm_source=im&utm_scene=person_space&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&utm_medium=im_card&corpId=dingf898ede2a6eeab33bc961a6cb783455b)
    
*   历史系分：[《\[20250224\]智能运维端到端+商家分3.0》](https://alidocs.dingtalk.com/i/nodes/1DKw2zgV2kZ3Rrapf1zN7RQqJB5r9YAn)
    
*   已有任务：[请至钉钉文档查看附件《收入直接相关任务列表》](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02me89hmqb59tlsckxl4i&rnd=0.0019112956181871477)
    
*   任务完成率看板：[https://fbi.alibaba-inc.com/dashboard/view/page.htm?spm=a2o1z.8189972.0.0.4a28543fQttJIz&id=1663738](https://fbi.alibaba-inc.com/dashboard/view/page.htm?spm=a2o1z.8189972.0.0.4a28543fQttJIz&id=1663738)
    
*   商户分层：[《商家成长分层口径》](https://alidocs.dingtalk.com/i/nodes/r1R7q3QmWew5lo02fG7d7lxxJxkXOEP2?cid=470019194%3A592264883&utm_source=im&utm_scene=person_space&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&utm_medium=im_card&corpId=dingd8e1123006514592)
    

## 1.1 建设目标

*   商户/门店阶段分层：新手、瓶颈、成长、成熟
    
*   不同阶段的任务体系：[请至钉钉文档查看附件《运维仪表盘-任务列表最终版》](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02me89jixkovecbwvqak&rnd=0.0019112956181871477)
    
*   监控任务平台的使用率、完成率
    

## 2. 业务流程

[请至钉钉文档查看「白板」](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mei8lufpkfadvuglms&rnd=0.0019112956181871477)

| **任务执行域** | **过程数据-现状** | **过程数据-预期** |
| --- | --- | --- |
| 代运营工作台 | ✅ 任务曝光埋点<br>✅ 任务点击埋点 |  |
| 企微/外呼 | ✅ 聊天内容<br>*   问题提取<br>    <br>*   运维小记分析 | *   [ ] 运维小记分析，追加任务执行分析<br>    <br>仅能到户维度，但基建任务大都在店维度，比较难关联到任务上 |
| 商品域 | ✅ 操作类型 + 操作时间 + 操作人 |  |
| 广告域 | ✅ 广告计划的操作记录（但不确定操作人id是否为轩辕id） | *   [ ] 广告充值等其余场景的操作记录补全<br>    <br>ps：++有升级想法，目前没需求++ |
| 装修域 | ✅ 仅记录了更新操作人 | *   [ ] （相册/公告/关键词）操作类型 + 操作时间 + 操作人<br>    <br>ps：++有计划，但优先级没排上++ |
| 内容域 | ✅ 最新一次操作记录（覆盖，非全量历史） | *   [ ] （手艺人/作品集/评价）操作类型 + 操作时间 + 操作人 |

## 3. 系统架构设计

### 3.1 任务定义

*   任务构成：++确定性任务 + 时效性任务 + 主管下达++。见：[请至钉钉文档查看附件《运维仪表盘-任务列表最终版》](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mf52f9e9bhv9uutfz5q&rnd=0.0019112956181871477)
    

[请至钉钉文档查看「白板」](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mf3dwm9jx3iz8ncvro&rnd=0.0019112956181871477)

时效性任务，按续充（区分首续、二充以上）、续签，下发 \[++复盘沟通++\] 任务。

### 3.2 作战地图

[请至钉钉文档查看「白板」](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mf52knjfez9wntjhk2r&rnd=0.0019112956181871477)

### 3.3 任务框架

[请至钉钉文档查看「白板」](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mf5fa3801l2up80yx1n&rnd=0.0019112956181871477)

### 3.4 口径定义

:::
任务生命周期消息topic：ODPS\_OPT\_TASK\_V2\_TOPIC

任务创建（下发）tag：

任务推荐过期tag：RECOMMEND\_EXPIRED

任务执行过期tag：EXECUTE\_EXPIRED

任务推荐 tag：

算法优先级回流 tag：

离线表口径：

组织架构/服务商/人员表：[https://dw.alibaba-inc.com/dmc/odps-table/odps.kbdw.dim\_gd\_sale\_employee\_bloc/](https://dw.alibaba-inc.com/dmc/odps-table/odps.kbdw.dim_gd_sale_employee_bloc/)

人户关系表：[https://dw.alibaba-inc.com/dmc/odps-table/odps.kbdw.dim\_kb\_sale\_employee\_partner\_relation/](https://dw.alibaba-inc.com/dmc/odps-table/odps.kbdw.dim_kb_sale_employee_partner_relation/)

人店关系表：[https://dw.alibaba-inc.com/dmc/odps-table/odps.kbdw.dim\_kb\_sale\_employee\_shop\_relation/](https://dw.alibaba-inc.com/dmc/odps-table/odps.kbdw.dim_kb_sale_employee_shop_relation/)

运维商户宽表：[https://dw.alibaba-inc.com/dmc/odps-table/odps.kbads.ads\_gd\_info\_sale\_operation\_partner\_summary\_ds/](https://dw.alibaba-inc.com/dmc/odps-table/odps.kbads.ads_gd_info_sale_operation_partner_summary_ds/)

门店->商户阶段；

运维门店宽表：[https://dw.alibaba-inc.com/dmc/odps-table/odps.kbads.ads\_gd\_info\_sale\_operation\_shop\_summary\_ds/](https://dw.alibaba-inc.com/dmc/odps-table/odps.kbads.ads_gd_info_sale_operation_shop_summary_ds/)

绩效数据表 $\color{#0089FF}{@李亮(秃贝)}$  $\color{#0089FF}{@潘戈强(晏晨)}$  $\color{#0089FF}{@刘涛(籁玟)}$ 提供给 $\color{#0089FF}{@余浩(萧启)}$ 

商户维度今日推荐任务标记 $\color{#0089FF}{@唐皓源(榆澄)}$  提供给 $\color{#0089FF}{@余浩(萧启)}$ 

门店阶段；

任务实例表：【前置依赖建表】

提供给算法的任务表：

算法回传的优先级表：
:::

| **商户宽表** |  |
| --- | --- |
| **字段** | **解释** |
| 新的商家分层 | kbads.ads\_gd\_info\_sale\_operation\_partner\_summary\_ds.merchant\_tag<br>新增字段获取商家分层：SKA/KA |
| 门店最高阶段 | 阶段排序：成熟>成长>瓶颈>新手<br>当前字段取值：商户有多家店，取最高阶段门店 |
| 门店所有阶段 | 商户有多家店所处阶段聚合，逗号分割，枚举code如下<br>newbie：新手<br>bottleneck：瓶颈<br>develop：成长<br>mature：成熟 |
| ARPU值 | 店均ARPU值 |

| **门店宽表** gd\_info\_cdm.dws\_gd\_info\_sale\_shop\_operate\_index\_nd |  |
| --- | --- |
| **字段** | **解释** |
| 阶段 | shop\_operate\_stage |
| 在约年费产品 | 当前签约是旺铺，还是商户通<br>biz\_channel 1:sht 2:wp |
| 商家分3.0基础分 | detail\_base\_score\_1d |
| 门店所处行业基础分总分 | detail\_base\_score\_max\_value\_1d |
| 货架有品数 | list\_spu\_cnt\_std    商品数 |
| 门店商品分 | detail\_goods\_score\_1d |
| 门店所处行业商品分总分 | detail\_goods\_score\_max\_value\_1d |
| 商家分等级 = 商家等级 | business\_score\_level\_1d |
| 五星货架达标 | is\_5\_star\_shelf |
| 门店ARPU值 |  |
| 是否实名 | 广告任务下发依赖<br>is\_phone\_verified |
| 30天在投 | 广告任务下发依赖<br>advertising\_shop\_cnt\_30d |

## 4. 各需求改动点

### 4.1 链路交互图

#### 4.1.1 任务框架

**任务实例表：本期新增表：****task\_instance\_v2**

**离线表：amap\_sales\_operation\_app\_task\_instance\_v2\_hour**

| 字段 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- |
| id | bigint | 主键 |  |
| env | varchar(8) | 环境标识 | pre预发 prod生产 |
| delete\_tag | varchar(16) | 删除标记 | 0未删除 1已删除 |
| gmt\_create | datetime | 创建时间 |  |
| gmt\_modified | datetime | 更新时间 |  |
| task\_no | bigint(20) | 任务id |  |
| task\_type | varchar(64) | 任务类型 | 本期新增任务枚举<br>```mysql<br>新增任务：<br>STORE_DECORATION-门店装修<br>SHELF_HAS_PRODUCTS-货架有品<br>PRODUCT_SCORE_COMPLIANCE-商品分达标<br> MERCHANT_LEVEL_UPGRADE_V2("MERCHANT_LEVEL_UPGRADE_V2", "商家等级V2"),<br>    MERCHANT_LEVEL_UPGRADE_V3("MERCHANT_LEVEL_UPGRADE_V3", "商家等级V3"),<br>    MERCHANT_LEVEL_UPGRADE_V4("MERCHANT_LEVEL_UPGRADE_V4", "商家等级V4"),<br>    MERCHANT_LEVEL_UPGRADE_V5("MERCHANT_LEVEL_UPGRADE_V5", "商家等级V5"),<br>FIVE_STAR_SHELF-  五星货架<br>PHONE_REAL_NAME_VERIFICATION-电话实名制<br>主管下发：<br>ADVERTISING_TASK-广告任务<br>INFRASTRUCTURE_TASK-基建任务<br>GROUP_BUYING_TASK-团购任务<br>MAJOR_PROMOTION_TASK-大促任务<br>PARK_RECRUITMENT-场域招商<br>CONTRACT_SIGNING_RENEWAL-签续任务<br>OTHER_TASK-其他任务<br>历史任务：<br>SHOP_ANNUAL_RENEW-续签复盘<br>AD_NEW_SIGN-新签上线沟通<br>AD_CONTINUED_CHARGING-首续复盘<br>AD_BALANCE_WARNING-续充任务<br>SHOP_RISK - 风控任务<br>STORE_REVIEW_COMPLAINT-门店评价客诉<br>``` |
| pid | varchar(64) | 户id | 户id   （必填） |
| shop\_id | varchar(64) | 店id | 店id |
| out\_biz\_id | varchar(64) | 外部id | 任务的第三层id<br>(一般为空)<br>差评场景下-评论id |
| status | varchar(32) | 任务状态 | 待处理/已完成/已过期/已失效<br>待处理：PROCESSING<br>已完成：FINISH<br>已过期：EXPIRED （在任务时效期内未流转为终态）<br>已失效：INVALID<br>特殊场景：<br>1.  目标完成，但非业务主动驱动，则任务为已失效，目标为已完成<br>    <br>2.  若户分层进阶/掉落，则原有任务状态为失效。若业务完成了，那么任务目标为已完成<br>    <br>3.  若门店已经不是目标门店，则将进行中的任务置为已过期 |
| target\_status | varchar(32) | 任务目标状态 | 未完成：INCOMPLETE<br>已完成：COMPLETED |
| gmt\_start | datetime | 任务开始时间 | 任务的创建时间 |
| gmt\_end | datetime | 任务结束时间 | 任务达到终态的时间(完成/过期/失效) |
| gmt\_expired | datetime | 任务过期时间 | 开始时间 + 有效天数（自然日） |
| ext\_info | text | 任务扩展信息 | 1.  任务发生改变时的原子指标，或流转的判断指标<br>    <br>*   atomMetrics <br>    <br>    *   shopOperateStage - 门店阶段 <br>        <br>1.  是否催办<br>    <br>2.  任务优先级： 1.业务的 2.算法的<br>    <br>3.  自定义任务信息：1.任务名称 2.任务时间 3.任务描述 |

```mysql
CREATE TABLE `task_instance_v2` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  `env` varchar(8) NOT NULL COMMENT '环境标，PRE/PROD',
  `is_deleted` tinyint(4) NOT NULL COMMENT '软删除标，0-未删除 1- 已删除',
  `task_no` bigint(20) unsigned NOT NULL COMMENT '任务号，唯一标识',
  `task_type` varchar(32) NOT NULL COMMENT '任务类型',
  `pid` varchar(32) NOT NULL COMMENT '户id',
  `shop_id` varchar(32) NOT NULL COMMENT '店id',
  `out_biz_id` varchar(32) NOT NULL COMMENT '外部id',
  `status` varchar(32) NOT NULL COMMENT '任务状态。PROCESSING/FINISH/EXPIRED/INVALID',
  `target_status` varchar(32) NOT NULL COMMENT '任务目标状态 未完成/已完成',
  `gmt_start` datetime NOT NULL COMMENT '任务开始时间',
  `gmt_expired` datetime NOT NULL COMMENT '任务过期时间',
  `gmt_end` datetime DEFAULT NULL COMMENT '任务终止时间',
  `ext_info` text COMMENT '任务扩展信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_no` (`task_no`),
  KEY `idx_pid_shop_id` (`pid`,`shop_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_pid_task_status` (`pid`,`task_type`,`status`),
  KEY `idx_pid_status_target_status` (`pid`, `status`, `target_status`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='运维任务实例表v2';
```

##### 4.1.1.1 任务定义  $\color{#0089FF}{@徐文洋(亦竟)}$ 

[请至钉钉文档查看「脑图」](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mednczo7i9uqrnsx1b&rnd=0.0019112956181871477)

注：

数据通过backend接口写入及更新，后面会配置成订正工具

com.amap.sales.operation.app.hsf.backend.TaskTemplateBackendHsf

入参：

```json
{
    "taskType": "STORE_DECORATION",
    "taskRuleInfoModels": [
        {
            "atagId": "DEFAULT",
            "atagName": "默认",
            "taskRuleDim": "SHOP",
            "taskName": "STORE_DECORATION",
            "taskDesc": "门店装修",
            "taskTimeRule": {
                "expiredDateType": "NATURE_DAY",
                "remindDateType": "WORK_DAY",
                "expiredPeriod": 7,
                "remindValidPeriod": 7
            },
            "taskExecuteRule": {
                "jumpUrlMap": {
                    "DEFAULT": "http://www.taobao.com"
                }
            }
        }
    ]
}
```

查询接口：

com.amap.sales.operation.domain.prioritytask.ability.TaskTemplateQueryAbility#queryByTypeNew

*   TODO  任务催办时效，节假日明确
    

##### 4.1.1.2 系统-离线任务下发  $\color{#0089FF}{@白琛曦(橙希)}$ 

任务归属：[请至钉钉文档查看附件《运维仪表盘-任务列表最终版》](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mf9wjzh5lijhlkcwxer&rnd=0.0019112956181871477)

任务类型：

[请至钉钉文档查看「脑图」](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mf9qxp8w0glt2qqa2bii&rnd=0.0019112956181871477)

系统离线任务包括：门店装修，货架有品，商品分达标，商家等级，五星货架，电话实名制 

**离线任务下发流程：**

```plaintext
1.数据收集：多个离线数据源向ODPS提供数据
2.数据处理：通过SQL聚合处理，将多个数据源的数据整合成一张离线任务表
3.消息发送：将原子指标和任务生成状态作为消息体发送给MetaQ
4.任务处理阶段：operation系统监听MetaQ消息，根据任务状态执行insert或update操作
5.完成阶段：处理完成后返回确认；将原子指标存入extInfo字段

SQL流程
1.一次扫描门店宽表，针对6类任务计算“期望状态”；再和任务表对比，输出需要“新增/更新/失效”的任务。
2.幂等：相同户+店+任务类型+状态已存在，则不输出。
3.只有进行中的任务会流转为完成或失效；已经存在进行中的任务，就不再生成进行中的任务。
* base：原始指标
* exist：“已存在的任务”
* decision：计算“是否适用”和“期望状态”
* flat_desired：把decision多个任务的指标，按行展开
* flat_filtered ： 过滤无进行中的完成任务
* invalid_candidates： 过滤阶段+目标商家 失效场景
* union_all：合并
* todo：幂等过滤
```

离线ODPS入口： 调度时间：T+1  每晚1点调度

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d572d56240c3a8ec118feb8db2a8cf9eecbd40d79518ea10dc2affc7282299d7f5282c8d55d9f25c93)

离线任务处理特殊点：

```mysql
1.创建任务模版：新增离线任务模版 - task_template  亦竟 前置新建所有任务模板，若不存在模版，则不生成任务
2.在离线指标针对31，33区分处理（biz_channel 1:sht33  2:wp31）
  * 商家等级  31:瓶颈期V2/成熟期V4   33:瓶颈期V3/成熟期V5    任务切换时，也需要失效原有任务
  * 五星货架  31无   33有
3.若同一个时间段内，新增&&完成了该任务，则不下发mq，不生成该任务
  （因为sql是基于是否满足各原子指标来判断的，所以应该不会出现此类场景）
4.此类任务均为非沟通类任务，所以任务完成==任务目标完成，故满足完成任务条件时，同时更新status + target_status字段为已完成状态
```

工程入口：SystemIssueCommonTaskMsgConsumer

```mysql
topic:ODPS_OPT_TASK_V2_TOPIC
tag:SYSTEM_ISSUE_COMMON_TASK

@AppSwitch(des = "端到端系统消息降级开关 true为降级，false为不降级，默认不降级", level = Switch.Level.p2)
  public static boolean taskSystemIssueDegrade = false;
```

商家等级任务：

```mysql
ext_info会存储原子指标字段：atomMetrics 
```

```mermaid
sequenceDiagram
    participant offline as 离线数据源
    participant odps as ODPS
    participant mq as MetaQ
    participant operation as amap-sales-operation
    participant db as MySQL

    offline ->> odps : 门店装修数据
    offline ->> odps : 货架有品数据
    offline ->> odps : 商品分达标数据
    offline ->> odps : 商家等级数据
    offline ->> odps : 五星货架数据
    offline ->> odps : 电话实名制数据

    odps ->> odps : SQL聚合处理生成离线任务表

    odps ->> mq : 发送离线任务消息原子指标和任务生成状态

    mq -->> operation : 监听消息获取任务信息
    operation ->> db : 查询当前任务是否存在
    alt 任务已存在
        operation ->> db : 根据任务状态执行update操作
    else 任务不存在
        operation ->> db : 执行insert操作
    end
    operation -->> mq : 处理完成
```

##### ******* 系统-离线任务下发  $\color{#0089FF}{@白琛曦(橙希)}$ 

实时任务包括：门店评价客诉  

离线任务包括：新签上线沟通，首续复盘任务，续充复盘任务，续签复盘任务

其中广告类任务：新签上线沟通，首续复盘任务，续充复盘任务

其中门店评价客诉为结果类任务，其余都是沟通类7任务

```plaintext
@AppSwitch(des = "任务升级灰度开关", level = Switch.Level.p2)
public static boolean taskInstanceV2 = false;
```

###### *******.1 门店评价客诉

*    核心入口：历史现状
    

1.差评任务触发流程 (CommentMsgConsumer)

*   监听评论消息 (MetaqConsumeMsgType.COMMENT)
    
*   差评创建 → 创建任务 → 状态设为INIT  
    
*   差评创建后-> 创建推送任务 
    
    *   删除差评 → 取消任务  → 状态设为CANCEL
        

2. 差评任务完成流程 (ReplyMsgConsumer)

*   监听回复消息 (MetaqConsumeMsgType.REPLY)
    
    *   差评回复 → 状态设为FINISH
        
*   **本期改动点：**
    

1.新增商家分层判断，仅满足：SKA/KA 需触发评价客诉任务，不满足分层不触发

● 业务表：data\_works\_sync.ext\_info

● 源数据：kbads.ads\_gd\_info\_sale\_operation\_partner\_summary\_ds.merchant\_tag

● 需修改ODPS语句，新增商家分层字段：SKA/KA

```plaintext
NEW_SIGN,
THIRTY_DAYS_AD_CASTING,
STOP_CASTING,
NET_ANNUAL_FEE,
CPS


SKA/KA
```

2.CommentMsgConsumer 兼容逻辑：

*   降级开关：
    
    *   开关关闭使用旧版本代码仅写入agent\_task      
        
    *   开关开启使用新版本代码仅写入task\_instance\_v2  
        
*   新旧通知兼容：
    
    *   agent\_task 的写流量在开关开启后全量暂停，但读流量保持现状
        
    *   任务关联兼容：
        
        *   历史逻辑agent\_task写入后，创建commom\_task触达任务，commom\_task的bizId是pid；extInfo的opt\_task\_id 为agent的no，optTaskIdList为agent的noList 
            
        *   V2逻辑：写入task\_instance\_v2  后，通过pid查询是否存在; 
            
            *   若不存在则新建commom\_task，bizId是pid，opt\_task\_id 为V2的no，optTaskIdList为V2的no；
                
            *   optTaskIdList为agent的noList + v2的noList（~~opt\_task\_id字段是历史逻辑兼容，本期废弃~~）
                
    *   任务触达兼容
        
        *   触达逻辑：ReverseTouchTaskProcessor
            
        *   根据commom\_task的optTaskIdList同时查询新旧两张表
            

3.CommentMsgConsumer

*   监听评论消息 (MetaqConsumeMsgType.COMMENT) 
    
*   监听到删除差评时，更新status + target\_status字段为已完成状态
    
*   特殊点：
    
    *   若存在删除操作，但无任务，则创建一个已完成的任务
        
    *   ~~若存在删除操作，但任务过期（不会有这种场景）~~
        

4.ReplyMsgConsumer

*   监听回复消息 (MetaqConsumeMsgType.REPLY)
    
*   差评回复时，更新status + target\_status字段为已完成状态
    

5.差评取值改变

*   历史 0.9 - 2.1
    
*   本期 0   -2    //. TODO 
    

```mermaid
sequenceDiagram
    participant CommentSystem as 评论系统
    participant MQ as MetaQ
    participant CommentConsumer as CommentMsgConsumer
    participant ReplyConsumer as ReplyMsgConsumer
    participant TaskService as 任务服务
    participant DataWorksSync as DataWorksSync服务
    participant DB as MySQL

    Note over CommentSystem, DB: 差评任务触发流程
    CommentSystem ->> MQ : 发送评论消息
    MQ -->> CommentConsumer : 监听COMMENT消息
    
    CommentConsumer ->> CommentConsumer : 解析消息，验证POI类型和评论状态
    
    alt 差评创建
        rect rgb(255, 200, 200)
            Note over CommentConsumer, DataWorksSync: 🔴 本期新增：商家分层过滤
            CommentConsumer ->> DataWorksSync : 查询商家分层信息
            DataWorksSync ->> DB : 查询data_works_sync.ext_info
            DataWorksSync -->> CommentConsumer : 返回分层信息
            
            alt 商家分层不满足条件
                CommentConsumer -->> MQ : 跳过处理
            end
        end
        
        CommentConsumer ->> TaskService : 创建差评任务+策略+提醒
        TaskService ->> DB : 保存任务数据
        CommentConsumer -->> MQ : 任务创建完成
        
    else 删除差评
        CommentConsumer ->> TaskService : 取消差评任务
        TaskService ->> DB : 更新任务状态为CANCEL
        CommentConsumer -->> MQ : 任务取消完成
    end
    
    Note over CommentSystem, DB: 差评任务完成流程
    CommentSystem ->> MQ : 发送回复消息
    MQ -->> ReplyConsumer : 监听REPLY消息
    
    ReplyConsumer ->> ReplyConsumer : 解析消息，验证商家账号
    ReplyConsumer ->> DB : 查询评论和门店信息
    ReplyConsumer ->> ReplyConsumer : 检查灰度策略匹配
    
    alt 灰度匹配通过且任务存在
        ReplyConsumer ->> TaskService : 完成差评任务
        TaskService ->> DB : 更新任务状态为FINISH
        ReplyConsumer -->> MQ : 任务完成
    else 灰度匹配失败或任务不存在
        ReplyConsumer -->> MQ : 跳过处理
    end
```

###### *******.2 续签复盘任务 （店维度，全周期任务）

ODPS离线表：odps.kbtech.amap\_sale\_operation\_shop\_renew\_task\_issue\_ds

工程入口：OptShopAnnualRenewTaskIssueMsgConsumer

```json
"topic": "ODPS_OPT_TASK_TOPIC",
"tag": "OPT_SHOP_ANNUAL_RENEW_TASK_ISSUE",
```

*   _调用signGw.queryLatestAnnualOrder(shopId)查询门店最新年费订单_
    
*   _通过checkShopCanRenew()方法判断门店是否可续签_
    
*   _续签任务创建条件：当前时间在合约最晚时间的前后90天内 （历史判断逻辑）_ 
    
    *   可续签期（±90天）：创建进行中任务，等待人工处理
        
    *   无需续签期（>90天）：创建已完成任务，记录处理结果
        
    *   已过期（<-90天）：不创建任务
        
*   生成任务/完成任务：task\_instance
    

本期改动：

1.  新增ODPS触发源，新增工程入口：SystemIssueRenewTaskMsgConsumer
    

2.按到期时间拆分为两套任务，且任务期间内只生成一次

*   续签任务-A处理流程：
    
    *   当距离到期 <= 60天时,查询是否已存在未达到终态的续签任务-A
        
    *   若不存在则新建任务A，任务状态为：PROCESSING
        
*   续签任务-B处理流程：
    
    *   当距离到期 < -60 天时触发（已超期60天），查询是否已存在未达到终态的续签任务-B
        
    *   若不存在则新建任务B，任务状态为：PROCESSING
        
*   目前ODPS枚举表满足，工程里实时查询门店订单，根据履约截止时间判断（promiseEndTime）（这里可以删除90天内到期）
    
*   //  31新增60天内过期的枚举
    

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d50b9eb06fce1af931cd4f3249698a24d26a768a8f8e9fa318fb0b52ebd5df15faa7817bc755435a5a)

2.新增双写开关：

*   降级开关：
    
    *   开关关闭使用旧版本代码仅写入task\_instance     
        
    *   开关开启使用新版本代码仅写入task\_instance\_v2   taskType = 'SHOP\_ANNUAL\_RENEW'
        
    *   能够写入v1生成任务的，一定能写入v2生成任务，故可以平替
        

3.任务目标完成状态更新：

*   若存在进行中的任务，并且到期时间 > 60天，则更新target\_status字段为已完成，status为已失效
    
*   若存在进行中的任务，但到期时间<= 60天，则表示任务进行中，不做任何处理
    

4.任务完成判断逻辑(实时判断)：

*   拜访小记沟通：（本期多个任务目标完成均依赖拜访小记，细节见4.1.1.8拜访小记整体）
    

5.特殊点：

*   若在任务过期后，才收到续签，则任务状态为已过期，任务目标为未完成（任务的过期状态由任务调度修改）
    

6.离线表改动：

*   更新为taskV2表
    

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d513f08c8cd8a78dd1c2dae34e37a77bb3061944385f09356b817dc0b169ab100b26ef34e5d8e35497)

```mermaid
sequenceDiagram
    participant MQ as 消息队列
    participant Consumer as OptShopAnnualRenewTaskIssueMsgConsumer
    participant VisitRecord as VisitRecordChangeMsgConsumer
    participant SignGw as 签约服务
    participant TaskQuery as 任务查询服务
    participant TaskCmd as 任务命令服务
    participant Lock as 分布式锁
    participant StaffGw as 员工关系服务

    MQ->>Consumer: 发送续签任务消息
    Consumer->>Consumer: 解析消息体(OptTaskIssueBody)
    
    alt 消息体无效
        Consumer-->>MQ: 结束处理
    end
    
    Consumer->>Consumer: 获取门店ID和商户ID
    Consumer->>SignGw: 查询门店最新签约订单
    SignGw-->>Consumer: 返回SignOrderModel
    
    Consumer->>Consumer: 计算距离到期天数
    
    %% 原有逻辑 - 保持蓝色
    alt 距离到期 > 90天
        Consumer->>Lock: 获取分布式锁
        Lock-->>Consumer: 锁获取成功
        Consumer->>TaskCmd: 创建已完成的任务实例
        TaskCmd-->>Consumer: 返回任务实例
        Consumer->>StaffGw: 查询门店运维小二
        StaffGw-->>Consumer: 返回员工ID
        Consumer->>TaskCmd: 完结任务
        TaskCmd-->>Consumer: 任务完结成功
        Consumer->>Lock: 释放锁
    end
    
    %% 新增逻辑1 - 续签任务A (红色标识)
    rect rgb(255, 200, 200)
        Note over Consumer: 🔴 新增逻辑：距离到期60天判断
        alt 距离到期 >= 60天
            Consumer->>TaskQuery: 查询是否已存在续签任务-A
            TaskQuery-->>Consumer: 返回任务列表
            
            alt 不存在续签任务-A
                Consumer->>Lock: 获取分布式锁
                Lock-->>Consumer: 锁获取成功
                Consumer->>TaskCmd: 创建续签任务-A
                TaskCmd-->>Consumer: 返回任务实例
                Consumer->>Lock: 释放锁
            else 已存在续签任务-A
                Consumer->>Consumer: 跳过，任务期间内只生成一次
            end
        end
    end
    
    %% 新增逻辑2 - 续签任务B (红色标识)
    rect rgb(255, 200, 200)
        Note over Consumer: 🔴 新增逻辑：超期30天判断
        alt 距离到期 < -30天
            Consumer->>TaskQuery: 查询是否已存在续签任务-B
            TaskQuery-->>Consumer: 返回任务列表
            
            alt 不存在续签任务-B
                Consumer->>Lock: 获取分布式锁
                Lock-->>Consumer: 锁获取成功
                Consumer->>TaskCmd: 创建续签任务-B
                TaskCmd-->>Consumer: 返回任务实例
                Consumer->>Lock: 释放锁
            else 已存在续签任务-B
                Consumer->>Consumer: 跳过，任务期间内只生成一次
            end
        end
    end
    
    %% 其他情况
    alt 其他情况
        Consumer->>Consumer: 不生成任务
    end
    
    Consumer-->>MQ: 消息处理完成

    Note over MQ, StaffGw: 续签任务完成判断逻辑 (本期新增)
    
    %% 任务完成逻辑 (本期新增)
    rect rgb(255, 200, 200)
        Note over VisitRecord, StaffGw: 🔴 本期新增：VisitRecordChangeMsgConsumer
        MQ->>VisitRecord: 发送投放记录变更消息
        VisitRecord->>VisitRecord: 解析消息体，验证投放记录类型
        
        alt 记录投放(一级选项)
            VisitRecord->>VisitRecord: 检测到投放记录
            VisitRecord->>TaskQuery: 查询相关续签任务
            TaskQuery-->>VisitRecord: 返回续签任务列表
            
            alt 存在续签任务且未完成
                VisitRecord->>Lock: 获取分布式锁
                Lock-->>VisitRecord: 锁获取成功
                VisitRecord->>TaskCmd: 标记续签任务已完成
                TaskCmd->>TaskQuery: 更新任务状态为FINISH
                TaskQuery-->>TaskCmd: 更新成功
                TaskCmd-->>VisitRecord: 任务完成处理成功
                VisitRecord->>Lock: 释放锁
            end
            VisitRecord-->>MQ: 投放记录完成处理
            
        else 上传投放方案
            VisitRecord->>VisitRecord: 检测到投放方案上传
            VisitRecord->>TaskQuery: 查询相关续签任务
            TaskQuery-->>VisitRecord: 返回续签任务列表
            
            alt 存在续签任务且未完成
                VisitRecord->>Lock: 获取分布式锁
                Lock-->>VisitRecord: 锁获取成功
                VisitRecord->>TaskCmd: 标记续签任务已完成
                TaskCmd->>TaskQuery: 更新任务状态为FINISH
                TaskQuery-->>TaskCmd: 更新成功
                TaskCmd-->>VisitRecord: 任务完成处理成功
                VisitRecord->>Lock: 释放锁
            end
            VisitRecord-->>MQ: 投放方案完成处理
        end
    end
```

###### *******.3 广告类任务（新签上线沟通，首续复盘任务，续充复盘任务，户维度任务）

*   现状梳理：
    
    *   ODPS离线表：odps.kbtech.amap\_sale\_operation\_ad\_task\_issue\_hh
        
    *   工程入口：OptMerchantAdTaskSyncMsgConsumer
        

```json
"topic": "ODPS_OPT_TASK_TOPIC",
"tag": "OPT_MER_AD_TASK_SYNC",
```

*   任务生成/完成逻辑 -- ODPS
    
    *   任务状态：
        
        *   ADD：广告系统有任务但运营系统没有 → 创建运营任务
            
        *   EXPIRE：运营系统有任务但广告系统任务过期 → 运营任务过期
            
        *   FINISH：运营系统有任务且广告系统任务完成 → 运营任务完成
            

*   任务类型：
    
    *   NO\_BALANCE\_RENEW → AD\_NO\_BALANCE\_CONTIUE (无余额续费)
        
    *   STOP\_RECALL → AD\_STOP\_EXPOSURE\_RECALL (停投召回)
        
    *   NEW\_SIGN\_ONLINE → AD\_NEW\_SIGN (新签上线 -- 对应本期新签上线沟通)
        
    *   CONTINUED\_CHARGING → AD\_CONTINUED\_CHARGING (持续投放 --对应本期首续复盘任务)
        
    *   BALANCE\_WARNING → AD\_BALANCE\_WARNING (余额预警 --对应本期续充任务)
        
*   本期改动：
    
    1.新增入口
    
    *   SystemIssueAdTaskMsgConsumer
        

2.修改任务目标完成：

*   数据来源：amap\_sale\_operation\_ad\_task\_issue\_hh 
    
*   过滤出进行中的任务
    
*   当广告系统任务状态为：FINISH时，更新target\_status字段为已完成，status为已失效（表示代运营侧任务目标完成，但非业务驱动，故任务失效）
    
*   当广告系统任务状态为：INVALID时，target\_status字段保持现状，status为已失效
    

3.任务完成判断逻辑(实时判断)：

*   拜访小记沟通：（本期多个任务目标完成均依赖拜访小记，细节见4.1.1.8拜访小记整体）
    
*   特殊点：在广告侧任务目标已经完成，但未沟通未记录拜访小记：那么任务目标为已完成，但任务状态：已失效
    

4.~~遗留问题：~~

*   ~~历史针对不同任务有特殊过滤~~
    
*   ~~比如新签上线任务中支持特殊行业：大休娱 + 美食 + 教培，关于extinfo：manageTimeLimit字段特殊赋值~~ 
    
*   ~~首续复盘有后续推送触发：MerchantCycleAutoPushTaskProcessor 根据商家分层和30再投发送提醒~~
    

5.判断户阶段，非当前任务的生成阶段时，忽略生成任务；

*   新手期：无
    
*   瓶颈期：新签上线 + 首续复盘 
    
*   成长期：新签上线 + 首续复盘  + 续充任务
    
*   成熟期：新签上线 + 首续复盘  + 续充任务
    

6.离线表改动：

本期新增，历史的下线

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d5a6a3e652134881234f64ec7ac5a8e9e2ad6a27d72a75dd9c794a6f003529b264d0ff23a5f0eeeeb8)

*   任务类型仅保留 新签上线 + 首续复盘 + 续充任务
    

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d5b75ef3c48078b9c2ea4fc2ed407b255220594a44d0a6130273702719f854adfbcf2ac2451b1280fd)

*   替换表2仅保留本期任务
    

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d56400afa3b575e8e28d2fcd62ed7bde56a2810446b5f58874388e650f9c4ebf5f476342b5ab40020d)

*   历史任务忽略，计划维度的任务整体下线
    
    *   AD\_RAISE\_BID\_PRICE  提出价任务
        
    *   AD\_RAISE\_DURATION  提投放时长任务
        

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d59d947bc62eefa984c1203e96700bf0b48a1aa9a1450708c89e8414fd673b1706ecede220208f05db)

*   新增商户阶段回退任务失效
    
*   忽略广告侧的过期状态，将adTask表的gmt\_create作为出参，+ 任务有效期 作为该任务的过期时间 
    
*   仅监听广告的ADD和FINISH状态  ADD-任务进行中 目标未完成   FINISH-任务已失效 目标已完成
    
    *   过期状态由过期脚本维护， 到达过期时间  EXPIRE - 任务已过期   目标保持现状
        
    *   失效状态依赖商户阶段回退 ，INVALID - 任务置为已失效，目标保持现状
        

```mermaid
sequenceDiagram
    participant AdSystem as 广告系统
    participant ODPS as ODPS系统
    participant MQ as MetaQ
    participant AdTaskSync as OptMerchantAdTaskSyncMsgConsumer
    participant VisitRecord as VisitRecordChangeMsgConsumer
    participant TaskService as 任务服务
    participant DB as MySQL

    Note over AdSystem, DB: 广告任务全生命周期管理

    Note over AdSystem, DB: 1. 数据源和任务生成
    AdSystem ->> AdSystem : 产出离线表<br/>amap_ad.task_process_detail_hourly
    AdSystem ->> ODPS : 推送广告任务数据
    ODPS ->> ODPS : SQL整合处理<br/>产出汇总表amap_sale_operation_ad_task_issue_hh
    ODPS ->> MQ : 发送任务同步消息<br/>OPT_MER_AD_TASK_SYNC
    MQ -->> AdTaskSync : 监听消息

    Note over AdSystem, DB: 2. 任务创建阶段
    AdTaskSync ->> AdTaskSync : 解析消息体，验证operate和taskType
    
    alt operate = ADD
        Note over AdTaskSync, DB: 广告系统有任务但运营系统没有
        AdTaskSync ->> AdTaskSync : 根据taskType判断任务类型
        
        alt taskType = AD_NEW_SIGN
            AdTaskSync ->> TaskService : 创建新签上线沟通任务
        else taskType = AD_CONTINUED_CHARGING
            AdTaskSync ->> TaskService : 创建首续复盘任务
        else taskType = AD_BALANCE_WARNING
            AdTaskSync ->> TaskService : 创建续充复盘任务
        end
        
        TaskService ->> DB : 插入任务实例
        AdTaskSync -->> MQ : 任务创建完成
        
    else operate = EXPIRE
        Note over AdTaskSync, DB: 运营系统有任务但广告系统任务过期
        AdTaskSync ->> TaskService : 设置任务过期
        TaskService ->> DB : 更新任务状态为EXPIRE
        AdTaskSync -->> MQ : 任务过期处理完成
        
    else operate = FINISH
        rect rgb(255, 200, 200)
            Note over AdTaskSync, DB: 🔴 本期改动：FINISH代表任务目标完成
            AdTaskSync ->> TaskService : 标记任务目标完成
            TaskService ->> DB : 更新任务状态为目标完成
            AdTaskSync -->> MQ : 任务目标完成处理
        end
    end

    Note over AdSystem, DB: 3. 任务完成阶段 (本期新增)
    rect rgb(255, 200, 200)
        Note over VisitRecord, DB: 🔴 本期新增：VisitRecordChangeMsgConsumer
        AdSystem ->> MQ : 发送投放记录变更消息
        MQ -->> VisitRecord : 监听投放记录变更
        
        alt 记录投放(一级选项)
            VisitRecord ->> VisitRecord : 检测到投放记录
            VisitRecord ->> TaskService : 标记任务已完成
            TaskService ->> DB : 更新任务状态为FINISH
            VisitRecord -->> MQ : 投放记录完成处理
            
        else 上传投放方案
            VisitRecord ->> VisitRecord : 检测到投放方案上传
            VisitRecord ->> TaskService : 标记任务已完成
            TaskService ->> DB : 更新任务状态为FINISH
            VisitRecord -->> MQ : 投放方案完成处理
        end
    end

    Note over AdSystem, DB: 4. 任务类型说明
    Note over AdSystem, DB: 新签上线沟通 = AD_NEW_SIGN<br/>首续复盘任务 = AD_CONTINUED_CHARGING<br/>续充复盘任务 = AD_BALANCE_WARNING
```

###### *******.4 风控任务 

工程入口：RiskMsgConsumer

```mysql
GD_PUNISH_CENTER_NOTIFY_EVENT
USER_WECHAT_NOTIFY
```

*   事件类型处理
    
    *   CREATE: 创建风控任务
        
    *   EFFECT: 生效处理，创建提醒任务
        
    *   REJECT: 拒绝申诉处理
        
    *   CANCEL: 取消处罚处理
        
*   任务管理
    
    *   创建运维任务实例
        
    *   创建提醒任务
        
    *   更新任务状态和参数
        
*   改动点
    
    1.降级开关：
    
    *   开关关闭使用旧版本代码仅写入task\_instance      
        
    *   开关开启使用新版本代码仅写入task\_instance\_v2 
        

```mermaid
sequenceDiagram
    participant MQ as 消息队列
    participant Consumer as RiskMsgConsumer
    participant ShopGw as 门店服务
    participant AppealGw as 申诉服务
    participant TaskQuery as 任务查询服务
    participant TaskCmd as 任务命令服务
    participant CommonTask as 通用任务服务
    participant StaffGw as 员工关系服务
    participant Lock as 分布式锁

    MQ->>Consumer: 发送风控消息(RiskBody)
    Consumer->>Consumer: 解析消息体并验证
    
    alt 消息体无效或关键字段为空
        Consumer-->>MQ: 结束处理
    end
    
    Consumer->>Lock: 获取分布式锁(caseId)
    Lock-->>Consumer: 锁获取成功
    
    Consumer->>ShopGw: 查询门店基础信息
    ShopGw-->>Consumer: 返回ShopBaseInfo
    
    alt 门店信息为空或商户ID为空
        Consumer-->>MQ: 结束处理
    end
    
    Consumer->>AppealGw: 查询申诉处罚信息
    AppealGw-->>Consumer: 返回AppealPunishInfo
    
    Consumer->>TaskQuery: 查询现有风控任务实例
    TaskQuery-->>Consumer: 返回TaskInstance列表
    
    Consumer->>Consumer: 根据事件类型处理
    
    %% 统一处理逻辑
    alt 事件类型 = CREATE
        Consumer->>Consumer: 创建新任务(如果不存在)
    else 事件类型 = EFFECT
        Consumer->>Consumer: 更新任务状态(允许申诉时)
    else 事件类型 = REJECT
        Consumer->>Consumer: 处理申诉结果(允许申诉时创建提醒，不允许时完结任务)
    else 事件类型 = CANCEL
        Consumer->>Consumer: 完结任务
    end
    
    Consumer->>TaskCmd: 执行任务操作(创建/更新/完结)
    TaskCmd-->>Consumer: 返回操作结果
    
    Consumer->>CommonTask: 创建/更新提醒任务
    CommonTask-->>Consumer: 返回提醒任务ID
    
    Consumer->>Lock: 释放分布式锁
    Consumer-->>MQ: 消息处理完成
```

##### ******* 主管-excel任务下发  $\color{#0089FF}{@白琛曦(橙希)}$ 

###### *******.1 批量下发

*   主管下发均为户维度任务
    
*   新增HSF接口：SupervisorTaskFacade.uploadSupervisorTask
    

入参 ：              

| 参数名 | 参数类型 | 参数示例说明 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| path | string | oss地址 | Y |  |
| taskType | string | 任务类型 | Y |  |
| taskName | string | 任务名称 | Y |  |
| taskDes | string | 任务描述 | Y |  |
| validDay | int | 任务实效 | Y | 0<validDay<= 30 |

bucket资源：

*   name:amap-sales-operation-prod
    
*   线上：路径地址：PROD/task/
    
*   预发：路径地址：PRE/task/
    
*   过期策略：3天
    

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d5792d118cd5964a7060b35b8c0c7a620b17acbc9968d085e823a911ea1b0ddb7bac0e75a7a4c88ee2)

*   读取oss文件流：[https://help.aliyun.com/zh/oss/developer-reference/getobject?spm=a2c4g.11186623.0.0.7ecf37b3OuNjmK#reference-ccf-rgd-5db](https://help.aliyun.com/zh/oss/developer-reference/getobject?spm=a2c4g.11186623.0.0.7ecf37b3OuNjmK#reference-ccf-rgd-5db)
    
*   使用EasyExcel进行文件流读取，并根据表单信息 + excel生成自定义任务
    
    *   最大支持读取1024KB大小的Excel文件 
        
    *   pid上限5000个
        
        ```mysql
                <dependency>
                    <groupId>com.alibaba</groupId>
                    <artifactId>easyexcel</artifactId>
                    <version>2.2.6</version>
                </dependency>
        ```
        
    
*   异步上传，解析后上传至common\_task
    
*   同步校验
    
    *   表头格式 
        
    *   表单必填字段
        
*   异步校验
    
    *   最大支持读取1024KB大小的Excel文件 ，若超过1024kb则报错
        
    *   上传的pid是否有关联的运维2
        
    *   若无运维2，则不合法，将失败原因上传至excel
        
*   相同数据上传两次
    
    *   接口频控：同一个操作id在xxs内仅允许调用一次
        
    *   正常业务的两次上传：生成独立的两次任务，彼此不受任务实效期和任务状态的影响（同一个pid上传两次相同的表单，生成两个相同的任务）
        
*   批量任务完成后下发通知
    

```plaintext
@AppSwitch(des = "主管上传任务钉钉通知小二模版", level = Switch.Level.p2)
public static String taskResultDingTalkTemplate = "您主管给您下达了【%s】，共包含【%s】个商户，请及时作业";
```
```mermaid
sequenceDiagram
participant FE as 前端
participant OSS as OSS
participant BE as 后端(同步接口)
participant Q as 异步队列/任务
participant WK as 异步Worker
participant SCH as 调度器

FE->>OSS: 直传Excel（预签名URL）
OSS-->>FE: 返回ossId/URL
FE->>BE: 提交 ossId/URL + 表单信息
BE->>BE: 表单参数校验（同步）
alt 校验失败
  BE-->>FE: 返回错误
else 校验通过
  BE->>Q: 投递异步任务（含ossId/URL + 表单）
  BE-->>FE: 立即返回成功（已受理）
end
WK->>OSS: getObject(bucket,key)
OSS-->>WK: InputStream
WK->>WK: EasyExcel解析 → 对象列表
WK->>WK: 结合表单校验与组装
WK->>WK: 插入 common_task（待调度）
SCH->>WK: 扫描并执行任务
```

###### *******.2 任务列表

*   新增HSF接口 ： SupervisorTaskFacade.querySupervisorTask
    

入参 ：              

| 参数名 | 参数类型 | 是否必填 | 参数示例说明 |
| --- | --- | --- | --- |
| taskNo | String | N | 任务id |
| taskType | String | N | 任务类型 |
| pageNo | int | Y | 分页参数 |
| pageSize | int | Y | 分页参数 |

出参：

| 参数名 | 参数类型 | 参数名 |
| --- | --- | --- |
| taskNo | string | 任务唯一标识 commom\_task的taskNo |
| starTime | String | 下发时间 |
| taskType | String | 任务类型 |
| taskName | String | 任务名称 |
| taskStatus | String | 下发状态（用于区分是否在异步处理阶段）<br>下发中：INCOMPLETE<br>下发完成：COMPLETED |
| succTotal | Long | 成功条数 （当taskStatus为下发完成时，该字段有值） |
| total | Long | 总条数 （当taskStatus为下发完成时，该字段有值） |
| downloadUrl | String | 可下载的httpUrl （当taskStatus为下发完成时，该字段有值） |

*   当前操作人只可查看自己下发的任务
    
*   ossUrl下载明细：
    
    *   pid
        
    *   是否成功
        
    *   失败原因
        
*   过期时间：180天
    

```plaintext
@AppSwitch(des = "主管上传任务明细文件过期时间，单位：天", level = Switch.Level.p2)
public static Integer taskResultDownloadUrlExpire = 180;
```
```mysql
{
  "success": true,
  "errorCode": "SUCCESS",
  "errorMsg": "操作成功",
  "model": {
    "dataList": [
      {
        "taskNo": "TASK_20250115_001",
        "startTime": "2025-01-15 12:00:00",
        "taskType": "ADVERTISING_TASK",
        "taskName": "广告任务批量下发",
        "taskStatus": "COMPLETED",
        "succTotal": 150,
        "total": 200,
        "downloadUrl": "https://oss.example.com/download/task_result_20250115_001.xlsx"
      } 
    ],
    "pageInfo": {
      "hasMore": false,
      "totalPage": 1,
      "currentPageNo": 1,
      "nextPageNo": 2,
      "pageSize": 10,
      "totalCount": 3
    }
  }
}
```

###### *******.3 任务枚举

使用统一的amap-sales-operation.OptConfigQueryHsf.queryPriorityTaskType

（1）催办入口 任务类型枚举

产品形态：

 ![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d5c65cf489601722b8bef2287d2ce555e3260220d6114d674968723a0c504bb91aa272217575243c9c)

入参 ：              

| 参数名 | 参数类型 | 是否必填 | 参数示例说明 |
| --- | --- | --- | --- |
| scene | String | Y | 场景<br>LEADER\_URGES -- 此处应该传这个，催办入口应该传这个 |
| 通用请求 |  |  |  |

出参

```mysql
{
  "success": true,
  "errorCode": "SUCCESS",
  "errorMsg": "操作成功",
  "model": [
    {
      "key": "STORE_DECORATION",
      "value": "门店装修"
      
    },
    {
      "key": "SHELF_HAS_PRODUCTS", 
      "value": "货架有品"
    },
    {
      "key": "PRODUCT_SCORE_COMPLIANCE",
      "value": "商品分达标"
    },
    {
      "key": "MERCHANT_LEVEL_UPGRADE_V2",
      "value": "商家等级V2"
    },
    {
      "key": "MERCHANT_LEVEL_UPGRADE_V3",
      "value": "商家等级V3"
    },
    {
      "key": "MERCHANT_LEVEL_UPGRADE_V4",
      "value": "商家等级V4"
    },
    {
      "key": "MERCHANT_LEVEL_UPGRADE_V5",
      "value": "商家等级V5"
    },
    {
      "key": "FIVE_STAR_SHELF",
      "value": "五星货架"
    },
    {
      "key": "PHONE_REAL_NAME_VERIFICATION",
      "value": "电话实名制"
    },
    {
      "key": "SHOP_ANNUAL_RENEW",
      "value": "续签复盘"
    },
    {
      "key": "AD_NEW_SIGN",
      "value": "新签上线沟通"
    },
    {
      "key": "AD_CONTINUED_CHARGING",
      "value": "首续复盘"
    },
    {
      "key": "AD_BALANCE_WARNING",
      "value": "续充任务"
    },
    {
      "key": "SHOP_RISK",
      "value": "风控任务"
    },
    {
      "key": "STORE_REVIEW_COMPLAINT",
      "value": "门店评价客诉"
    }
  ]
}
```

~~（2）SupervisorTaskFacade.taskTypeList~~

```mysql
{
  "success": true,
  "errorCode": "SUCCESS",
  "errorMsg": "操作成功",
  "model": [
    {
      "key": "ADVERTISING_TASK",
      "value": "广告任务"
      
    },
    {
      "key": "INFRASTRUCTURE_TASK", 
      "value": "基建任务"
    },
    {
      "key": "GROUP_BUYING_TASK",
      "value": "团购任务"
    },
    {
      "key": "MAJOR_PROMOTION_TASK",
      "value": "大促任务"
    },
    {
      "key": "PARK_RECRUITMENT",
      "value": "场域招商"
    },
    {
      "key": "CONTRACT_SIGNING_RENEWAL",
      "value": "签续任务"
    },
    {
      "key": "OTHER_TASK",
      "value": "其他任务"
    }
  ]
}
```

##### ******* 任务推荐  $\color{#0089FF}{@唐皓源(榆澄)}$ 

###### *******.1 离线清洗发送mq推荐任务

**推荐任务清洗规则：**

（1）任务状态 =【待处理】，任务目前不处于 推荐 期间

（2）任务归属于小二有运维关系的所有pid下的所有任务

（3）分层商户推荐数量阈值要求：低频— 任务15；高频KA、SKA—任务 30；高频中长尾— 任务 45

（4）推荐规则：参见下一节的综合推荐优先级

*   SQL示意
    

```sql
-- 创建推荐任务清洗表
CREATE TABLE IF NOT EXISTS amap_sale_operation_system_recommend_task_ds
(
    employee_id     STRING COMMENT '员工ID'
    ,partner_id     STRING COMMENT '商户ID（逗号分隔）'
    ,task_no        STRING COMMENT '任务号（逗号分隔）'
    ,metaq_body     STRING COMMENT 'metaQ消息体'
)
COMMENT '代运营-系统推荐任务'
PARTITIONED BY 
(
    dt              STRING
)
LIFECYCLE 7
;

-- 插入推荐任务清洗数据
INSERT OVERWRITE TABLE amap_sale_operation_system_recommend_task_ds PARTITION (dt = '${bizdate}')
SELECT  employee_id
        ,partner_id
        ,task_no
        ,metaq_body
FROM    (
    -- 最终聚合：每个员工一条记录
    SELECT  employee_id
            ,CONCAT_WS(',', COLLECT_LIST(DISTINCT partner_id)) as partner_id
            ,CONCAT_WS(',', COLLECT_LIST(task_no)) as task_no
            ,map_str_json("employeeId", employee_id, "taskNos", CONCAT_WS(',', COLLECT_LIST(task_no))) as metaq_body
    FROM    (
        -- 根据商户标签动态获取不同数量的任务
        SELECT  employee_id
                ,partner_id
                ,task_no
                ,comprehensive_priority
                ,merchant_tag
                ,task_limit
                ,ROW_NUMBER() OVER (PARTITION BY employee_id ORDER BY comprehensive_priority DESC) as task_rank
        FROM    (
            -- 计算综合优先级并获取商户标签
            SELECT  emp_rel.employee_id
                    ,emp_rel.partner_id
                    ,task.task_no
                    ,task.priority
                    ,task.task_type
                    ,partner_summary.merchant_tag
                    ,-- 根据商户标签确定任务数量限制
                    CASE 
                        WHEN partner_summary.merchant_tag = '低频' THEN 15
                        WHEN partner_summary.merchant_tag IN ('高频', 'ka', 'ska') THEN 30
                        WHEN partner_summary.merchant_tag = '高频中长尾' THEN 45
                        ELSE 15  -- 默认值
                    END as task_limit
                    ,-- 归一化priority: (priority - min_priority) / (max_priority - min_priority)
                    (task.priority - min_priority.min_p) / (max_priority.max_p - min_priority.min_p) as normalized_priority
                    ,-- 综合优先级 = 归一化priority - task_type优先级
                    (task.priority - min_priority.min_p) / (max_priority.max_p - min_priority.min_p) - CAST(task.task_type AS DOUBLE) as comprehensive_priority
            FROM    (
                -- 获取员工商户关系
                SELECT  employee_id
                        ,partner_id
                FROM    odps.kbdw.dim_kb_sale_employee_partner_relation
                WHERE   new_relation_type = 'ADVERTISE_OPERATE'
            ) emp_rel
            INNER JOIN (
                -- 获取商户标签信息
                SELECT  partner_id
                        ,merchant_tag
                FROM    kbads.ads_gd_info_sale_operation_partner_summary_ds
                WHERE   ds = '${bizdate}'
            ) partner_summary
            ON      emp_rel.partner_id = partner_summary.partner_id
            INNER JOIN (
                -- 获取任务实例数据（最新分区）
                SELECT  task_no
                        ,biz_id
                        ,priority
                        ,task_type
                FROM    kbtech.amap_sales_operation_task_intance_xxx
                WHERE   dt = (
                    SELECT  MAX(dt) 
                    FROM    kbtech.amap_sales_operation_task_intance_xxx
                )
                AND     status = 'INIT'  -- 只取初始状态的任务
                AND     (ext_info IS NULL OR GET_JSON_OBJECT(ext_info, '$.recommend_type') IS NULL OR GET_JSON_OBJECT(ext_info, '$.recommend_type') = '')  -- recommend_type为空
            ) task
            ON      emp_rel.partner_id = task.biz_id
            CROSS JOIN (
                -- 计算全局最小priority
                SELECT  MIN(priority) as min_p
                FROM    kbtech.amap_sales_operation_task_intance_xxx
                WHERE   dt = (
                    SELECT  MAX(dt) 
                    FROM    kbtech.amap_sales_operation_task_intance_xxx
                )
                AND     status = 'INIT'  -- 只取初始状态的任务
                AND     (ext_info IS NULL OR GET_JSON_OBJECT(ext_info, '$.recommend_type') IS NULL OR GET_JSON_OBJECT(ext_info, '$.recommend_type') = '')  -- recommend_type为空
            ) min_priority
            CROSS JOIN (
                -- 计算全局最大priority
                SELECT  MAX(priority) as max_p
                FROM    kbtech.amap_sales_operation_task_intance_xxx
                WHERE   dt = (
                    SELECT  MAX(dt) 
                    FROM    kbtech.amap_sales_operation_task_intance_xxx
                )
                AND     status = 'INIT'  -- 只取初始状态的任务
                AND     (ext_info IS NULL OR GET_JSON_OBJECT(ext_info, '$.recommend_type') IS NULL OR GET_JSON_OBJECT(ext_info, '$.recommend_type') = '')  -- recommend_type为空
            ) max_priority
        ) priority_calc
        WHERE   priority_calc.comprehensive_priority IS NOT NULL
    ) ranked_tasks
    WHERE   task_rank <= task_limit  -- 根据商户标签动态限制任务数量
    GROUP BY employee_id
) final_data
```

**mq配置：**topic=OPT\_RECOMMEND\_TASK      tag=SYSTEM\_OPT\_RECOMMEND\_TASK    ConsumerId: CID\_amap-sales-operation-metaq

++每日离线清洗发送的任务id是，每个小二下面，当天推荐优先级top15/30/45的待处理任务，且不处于推荐期间的++

###### *******.2 综合加权推荐优先级计算

 加权推荐综合分数  = 

w1 \* ++归一化算法收入预测（如果值为0，那么该值为0）++ +

w2 \* 业务通排分层优先级（每个期内，改值最低的为1，最高的为期内任务种类数） 

w3 \* 归一化（coef\_efficiency\*coef\_urgency） ++（如果值为0，那么该值为0）++

待确认 具体的算法口径

具体做法：额外left join一张用于ab桶实验和圈选小二的表，

表为小二id ab桶标识符 具体规则

如果没有匹配上，使用兜底值 

 ++归一化算法，以收入为例子++：(任务收入- min任务收入) /（max任务收入-min任务收入）  ～  \[0, 1\]（max == min的时候，归一化优先级参数为0）

 **推荐顺序按照加权推荐****综合分数降序排列，分数高的被推荐**

###### *******.3 推荐任务写入

推荐任务流程图

```plantuml
@startuml 任务推荐写入
title 任务推荐写入流程图

participant "ODPS离线清洗" as odps
participant "MetaQ消息队列" as metaq
participant "amap-sales-operation\napp层" as app
participant "amap-sales-operation\ndomain层" as domain
participant "amap-sales-operation\ninfra层" as infra
participant "Saro引擎" as saro
participant "MySQL数据库\ntask_instance_v2表" as mysql

== 1. 离线数据清洗 ==

odps -> metaq: 发送MetaQ消息
note right: topic=OPT_RECOMMEND_TASK\ntag=SYSTEM_OPT_RECOMMEND_TASK

== 2. 消息监听与解析 ==
metaq -> app: 消息消费
note right: 监听tag=SYSTEM_OPT_RECOMMEND_TASK

app -> app: 解析消息体
note right: employee_id（运维小二ID）\n参数INCOME_VALUE、VALUE_PRIORITY、COEF_URGENCY、COEF_EFFICIENCYEXPOSURE_INC\n和推荐任务ID列表（保持顺序存储）

== 3. 业务逻辑处理 ==
app -> domain: 执行任务推荐逻辑
note right: 传递employee_id和推荐任务ID有序列表

domain -> infra: 构建Saro查询条件
note right: 根据employee_id查询recommend_task_ids字段

infra -> saro: 查询推荐任务ID
note right: 查询条件：operation_staff_id = employee_id\nrecommend_task_ids字段不为空

saro -> infra: 返回recommend_task_ids,商户个数marchantCount
note right: 返回格式：task1,task2,task3\n（逗号分隔的字符串）

infra -> domain: 返回任务ID列表
note right: 解析字符串为目前已经推荐任务列表

domain -> infra: 查询任务实例
note right: 根据目前已经推荐任务task_id列表查询task_instancev2表

infra -> mysql: 查询任务实例数据
note right: 查询条件：task_no IN (task_id列表)\nAND is_deleted = 0

mysql -> infra: 返回推荐任务实例列表
note right: 返回字段：task_no,ext_info等

infra -> domain: 返回任务实例数据

domain -> domain: 统计推荐任务实例个数recommendTaskInstanceCount

domain -> domain: 判断是否达到阈值
note right: 判断条件：\n商户个数marchantCount >= 商户阈值 OR\n任务个数recommendTaskInstanceCount >= 任务阈值

alt 已达到阈值
    domain -> app: 返回处理完成
    note right: 业务逻辑结束，无需处理
else 未达到阈值
    domain -> domain: 遍历推荐任务ID列表
    loop 遍历每个任务实例
        note right: 检查ext_info中的recommendType字段
        alt recommend_type不为空
            domain -> domain: 跳过该任务
            note right: 商户count和任务count都不加
        else recommendType为空
            domain -> domain: 更新任务实例
            note right: 更新ext_info的recommendType字段
            domain -> infra: 执行更新操作
            infra -> mysql: 更新task_instanceV2表（写入推荐类型、过期时间、优先级分数、预计曝光量、预计收入提高等等）
            note right: 更新ext_info字段
            
            mysql -> infra: 返回更新结果
            infra -> domain: 返回更新结果
            domain -> domain: 更新统计计数
            note right: 统计逻辑：\n- 检查更新商户PID是否在列表中\n- 更新商户count和任务count
            
            domain -> domain: 重新判断阈值
            note right: 判断是否达到阈值
            alt 已达到阈值
                domain -> domain: 停止处理
                note right: 跳出循环，业务逻辑结束
            else 未达到阈值
                domain -> domain: 继续处理下一个任务
            end
        end
    end
end

@enduml

```

###### *******.4 算法回流数据写入

```plaintext
OPT_TASK_PRIORITY_SYNC("ODPS_OPT_TASK_TOPIC", "OPT_TASK_PRIORITY_SYNC_V2", "任务优先级同步v2"),

```

##### ******* 任务催办  $\color{#0089FF}{@唐皓源(榆澄)}$ 

###### *******.1 催办任务数据聚合

人户引擎改造

预发saro：http://saro.alibaba-inc.com/eflow/editor/e-85vr6680o3x4/edit

线上saro：http://saro.alibaba-inc.com/eflow/editor/e-ucxp8gf43zzg/edit

[请至钉钉文档查看「白板」](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02m7ladh2ydg6qr2b62tw&rnd=0.0019112956181871477)

*   改动点：关联新任务表，解析任务表中任务特性（主管下达、主管催办、主管推荐）
    
*   加工人户引擎新增供查询字段
    

| key | 详细说明 |
| --- | --- |
| todo\_task\_types | 待办任务类型 |
| recommend\_task\_ids | 推荐的任务id（推荐来源可能是 主管下达、主管催办、系统推荐） |
| arpu | 商户arpu值 |

###### *******.2 点击去催办后，诊断建议展示 $\color{#0089FF}{@唐皓源(榆澄)}$ 

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d5896301035cdaf8f08698a2e8191fd30487fa9d0265d07efb80598540be3faffa80d13df8850e7a52)

提供facade接口 ，接口名：amap-sales-operation.AgentOperationQueryFacade.queryEmployeeAnalysis

入参请求名： 

| 参数名 | 参数类型 | 参数示例说明 |
| --- | --- | --- |
| staffId | String | 小二id（和服务商id二选一） |
| companyId | String | 服务商id（和小二id二选一） |
| 其他通用参数 |  |  |

出参：

```json
{
    "result": true,
    "code": "00000",
    "message": null,
    "version": "1.0",
    "timestamp": "1753099894907",
    "success": true,
    "msgInfo": "调用成功",
    "msgCode": "SUCCESS",
    "traceId": "0bfb408b17530998928682094e162f",
    "data": {
        "result": true,
        "traceId": "0bfb408b17530998928682094e162f",
        "code": "1",
        "layerDistribution": [
            {
                "layerName": "新手期",
                "layerCode": "NEWBIE",
                "shopCount": 3,
                "taskCount": 8
            },
            {
                "layerName": "成长期",
                "layerCode": "GROWTH",
                "shopCount": 3,
                "taskCount": 7
            },
            {
                "layerName": "瓶颈期",
                "layerCode": "BOTTLENECK",
                "shopCount": 2,
                "taskCount": 6
            },
            {
                "layerName": "成熟期",
                "layerCode": "MATURE",
                "shopCount": 1,
                "taskCount": 3
            }
        ]
        "diagnose":"" // 诊断建议 四个合在一起 
        "success": true,
        "message": null,
        "msgInfo": "调用成功",
        "version": "1.0",
        "msgCode": "SUCCESS",
        "timestamp": "1753099894900"
    }
}
```

###### *******.3 根据任务类型，筛选展示任务（商户聚合，供催办）

产品形态：

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d5b18f8ed26dd20392338383834b848f9f23a49236084b03d22faca0af1ff762cbe0486864959edfa8)

流程图：

```plantuml
@startuml 系统架构流程图

title 运维小二待办任务详情查询系统架构流程图

participant "前端系统" as frontend
participant "amap-sales-operation\napp层" as app
participant "amap-sales-operation\ndomain层" as domain
participant "amap-sales-operation\ninfra层" as infra
participant "HA3搜索引擎" as ha3
participant "MySQL数据库\ntask_instance表" as mysql

== 接口调用阶段 ==
frontend -> app: 1. queryOptTodoTask(request)
note right: 请求参数：\n- employeeId: 运维小二ID\n- taskType: 任务类型\n- page: 分页信息

== 参数校验阶段 ==
app -> app: 2. 参数校验
note right: 校验内容：\n- 运维小二ID非空\n- 任务类型有效性\n- 分页参数合法性\n- 操作员信息完整性

alt 参数校验失败
    app -> frontend: 返回参数错误信息
else 参数校验通过
    == 业务逻辑处理阶段 ==
    app -> domain: 3. 调用领域层业务逻辑
    note right: 传递查询条件和操作员信息
    
    == HA3引擎查询阶段 ==
    domain -> infra: 4. 构建HA3查询条件
    note right: 查询条件：\n- operation_staff_id: 运维小二ID\n- todo_task_labels: 任务类型标签\n- 分页参数
    
    infra -> ha3: 5. 执行HA3查询
    note right: 查询场景：MERCHANT_RELATIONAL\n查询字段：merchant_id, merchant_name
    
    ha3 -> infra: 6. 返回商户PID列表
    note right: 返回结果：\n- 商户ID列表\n- 商户名称列表

    
    == 数据库查询阶段 ==
    infra -> mysql: 7. 查询task_instance_v2表
    note right: 查询条件：\n- pid IN (商户PID列表)\n- task_type = 指定任务类型\n- is_deleted = 0\n status = PROCESSING \n ext_info里面
    
    mysql -> infra: 8. 返回任务实例数据
    note right: 返回字段：\n- biz_id: 商户PID\n- sub_biz_id: 门店ID\n- task_type: 任务类型\n- status: 任务状态
    
    == 数据统计阶段 ==
    infra -> infra: 9. 数据统计处理
    note right: 统计逻辑：\n- 按商户PID分组\n- 统计门店数量(shop_id不为空)\n- 统计任务总数量\n- 获取商户名称
    
    == 数据组装阶段 ==
    infra -> domain: 10. 返回统计结果
    note right: 数据内容：\n- 商户名称\n- 商户PID\n- 门店数量\n- 任务总数量
    
    domain -> app: 11. 返回业务数据
    note right: 业务对象：\nOptTodoTaskDTO列表
    
    == 响应返回阶段 ==
    app -> frontend: 12. 返回分页结果
    note right: 响应格式：\nResultDTO<PageDTO<OptTodoTaskDTO>>\n包含分页信息和商户详情列表
end

@enduml

```

任务筛选  任务状态待处理 && 任务不在主管催办期间 && 不是主管下达的任务 

**接口：**amap-sales-operation.OptTaskManageFacade.queryOptTodoTask

**入参 ：OptTodoTaskQueryRequest 请求参数**

| 参数名 | 参数类型 | 参数示例说明 |
| --- | --- | --- |
| employeeId | String | 运维小二ID，如："12345" |
| taskType | String | 任务类型枚举 |
| page | PageRequest | 分页信息 |
| 其他通用参数 |  |  |

出参 ： ResultDTO<PageDTO<OptTodoTaskDTO>>

OptTodoTaskDTO说明：

| 参数名 | 参数类型 | 参数示例说明 |
| --- | --- | --- |
| merchantName | String | 商户名称，如："北京某某餐饮有限公司" |
| merchantId | String | 商户PID，如："2088123456789012" |
| shopCount | Integer | 该商户下有该任务、未完成的门店数量，如：5 |
| totalTaskCount | Integer | 该商户下任务总数量，如：12 |

###### *******.4 催办任务提交

**接口：**amap-sales-operation.OptTaskManageFacade.remindOptTask

```plantuml
@startuml 异步催办任务流程图


title 异步催办任务流程图

participant "客户端" as client
participant "amap-sales-operation\napp层" as app
participant "amap-sales-operation\ndomain层" as domain
participant "amap-sales-operation\ninfra层" as infra
participant "线程池" as executor
participant "MySQL数据库\ntask_instance表" as mysql


client -> app: remindOptTask(RemindOptTaskRequest)
note right: 入参：商户PID列表，任务类型，操作员信息


app -> app: 参数校验
note right: 校验商户PID、任务类型、操作员信息合法校验

alt 参数校验失败
    app -> client: 返回参数错误
else 参数校验成功

    app -> domain: 启动异步催办任务
    note right: 使用CompletableFuture异步处理
    
    domain -> executor: 提交异步任务
    note right: CompletableFuture.supplyAsync(() -> {\n  // 异步催办逻辑\n}, urgeTaskExecutor)
    

    domain -> app: 返回催办提交成功
    note right: 不等待异步任务完成
    
    app -> client: 返回操作结果
    note right: ResultDTO<Boolean>\ntrue表示催办提交成功
end

executor -> domain: 执行催办任务操作
note right: 调用领域层业务逻辑

domain -> infra: 查询商户下的所有任务
note right: 根据商户PID查询task_instance_v2表

infra -> mysql: 查询任务实例
note right: 查询条件：biz_id = 商户PID，task_type = 任务类型，is_deleted = 0

mysql -> infra: 返回任务列表


infra -> infra: 构建催办信息
note right: 构建ext_info更新内容：\n计算recommendExpiredTime: 过期时间，过滤节假日\nrecommendType:推荐来源，remind—主管催办

infra -> mysql: 批量更新任务ext_info

mysql -> infra: 返回更新结果
note right: 返回更新成功的记录数

infra -> domain: 返回更新结果
note right: 返回是否更新成功

domain -> executor: 返回业务结果
note right: 返回催办操作结果


executor -> executor: 记录处理日志
note right: 记录催办成功的任务数量
@enduml

```

入参 ：RemindOptTaskRequest 请求参数 

| 参数名 | 参数类型 | 参数示例说明 |
| --- | --- | --- |
| merchantIdList | List<String> | 商户PID列表，如：\["2088123456789012", "2088765432109876"\] |
| taskType | String | 任务类型枚举 |
| 其他通用参数 |  |  |

##### 4.1.1.7 离线驱动任务完成  $\color{#0089FF}{@白琛曦(橙希)}$ 

*   基建、广告新签、续充、实名制、续签
    

\-- 和前置的任务生成逻辑收拢在一起，梳理了整个任务的生命周期改动

##### 4.1.1.8 实时驱动任务完成  $\color{#0089FF}{@白琛曦(橙希)}$ 

新增工程入口：VisitRecordChangeMsgConsumer

```mysql
新增 topic：VISIT_RECORD_CHANGE

## VISIT_RECORD_CHANGE MetaQ Topic##
spring.metaq.consumers[0].topics[25]=VISIT_RECORD_CHANGE
spring.metaq.consumers[0].sub-expressions[25]=*
```

*   新增降级开关，开关开启，则直接消费成功
    

```mysql
    @AppSwitch(des = "拜访小记消息降级开关 true为降级，false为不降级，默认不降级", level = Switch.Level.p2)
    public static boolean visitRecordChangeMsgDegrade = false;
```

*   仅处理代运营场景-新增配置
    

```mysql

    @AppSwitch(des = "拜访消息业务场景", level = Switch.Level.p2)
    public static List<String> visitBizScene = new ArrayList<>();
    static {
        visitBizScene.add("C33_AGENT_OUT_CALL");
        visitBizScene.add("WECHAT_AGENT");
        visitBizScene.add("ASSIGNMENT_TASK"); -- 主管任务
    }
```

*   特殊点：
    
    *   在广告侧任务目标已经完成，但未沟通未记录拜访小记：那么任务目标为已完成，但任务状态：已失效
        
    *   拜访小记是pid维度，若pid下有多个shopId任务，则认为均完成
        

*   拜访记录驱动任务完成：
    

| 任务类型 | 任务类型 | 完成条件 | 备注 |
| --- | --- | --- | --- |
| 新签上线任务 | 广告类 | 运维小记记录投放结果，至少记录到【一级选项】，或者上传投放方案，记为任务完成 | 广告类任务-code对应表：<br>[请至钉钉文档查看附件《运维仪表盘-任务列表最终版》](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mfaymrs8bhm1wq02t37&rnd=0.0019112956181871477)<br>![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d59c4a718d4280b9ac7c29f91f9b4e6d255f1f6536ad0fa9869976111b61f8e8804c4dda9f954150fe) |
| 首续复盘任务 |  | 运维小记记录沟通结果视为任务完成，至少记录到【一级选项】 |
| 续签复盘任务 |  | 运维小记记录沟通结果视为任务完成，至少记录到【一级选项】——<br>需新增【部分续签成功】 |

| 续充复盘任务 |  | 运维小记记录沟通结果视为任务完成，至少记录到【一级选项】 |
| 主管下发任务 | 自定义任务 | 1.  **商户意向：**必填，愿意配合、不愿意配合、无明确反馈；（对应任务状态-只要有值，则任务已完成）<br>    <br>2.  **完成情况：**非必填，任务已完成、任务无法完成（对应目标状态-只要有值，则目标已完成） | 主管类任务-code对应表：<br>[《拜访小记系分》](https://alidocs.dingtalk.com/i/nodes/G53mjyd80KXOR2zkfQKLeyL7J6zbX04v?cid=69793679221&utm_source=im&utm_scene=team_space&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&utm_medium=im_card&corpId=dingf898ede2a6eeab33bc961a6cb783455b)<br>*   一个拜访id 可能会查到两个拜访记录，有任意“**商户意向**”或任意“**完成情况**” 有值，则认为有值 |

拜访库表：

```mysql
kbt_merchant_content_service@33.58.3.56:3001 [rm-8vbafc1givyp29fgo]
SELECT * FROM `visit_record` WHERE `visit_record_id` = '202409132884200214779'
SELECT * FROM `visit_record` WHERE `target_id` = '2088521305152051' ORDER BY id desc  limit 10
```

拜访字段：

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d594d124f29b76966f514ee9c1d53cb37b33c2ffdd89241f9cd7b7898179c701097db76e875034da91)

拜访配置：

```mysql
 @AppSwitch(des = "任务模版类型和拜访事项类型的映射关系V2", level = Switch.Level.p2)
    public static Map<String,List<String>> taskTempTypeAndVisitBizCodeMapV2 =new HashMap<>();
    static {
        taskTempTypeAndVisitBizCodeMap.put("BUILD_RELATIONSHIP", Arrays.asList("op_connection_purpose"));
        taskTempTypeAndVisitBizCodeMap.put("DEMAND_FOCUS", Arrays.asList("op_focus_needs"));
        taskTempTypeAndVisitBizCodeMap.put("COMPLIANCE_TRAINING", Arrays.asList("op_fulfill_train"));
        taskTempTypeAndVisitBizCodeMap.put("STORE_INFRASTRUCTURE", Arrays.asList("op_basic_setup_ready"));
        taskTempTypeAndVisitBizCodeMap.put("AD_TOU_FANG_CONFIRM", Arrays.asList("op_put_result"));
        taskTempTypeAndVisitBizCodeMap.put("NEW_SIGN_FU_PAN", Arrays.asList("op_review_tool"));
        taskTempTypeAndVisitBizCodeMap.put("FU_PAN", Arrays.asList("op_review_tool"));
        taskTempTypeAndVisitBizCodeMap.put("AD_EXTENSION", Arrays.asList("op_followup_recharge","op_followup_recharge_food"));
        taskTempTypeAndVisitBizCodeMap.put("NIAN_FEI__EXTENSION", Arrays.asList("op_followup_renew"));
        taskTempTypeAndVisitBizCodeMap.put("USP_OPTIMIZE", Arrays.asList("op_usp_item","op_usp_content"));
        taskTempTypeAndVisitBizCodeMap.put("STOP_DROPPING", Arrays.asList("op_recall_followup"));
    }
```

*   拜访小记的创建和修改均认为是完成该任务
    

```mermaid
graph TD
    A[开始处理] --> B[获取visitRecordId]
    B --> C[调用visitRecordQueryFacadeHsfGw.queryVisitRecord]
    C --> D{拜访记录是否存在?}
    D -->|否| E[记录日志: 查询拜访记录为空]
    D -->|是| F[解析拜访记录数据]
    
    F --> G[调用queryVisitFiledBizCode<br/>获取拜访字段列表]
    F --> H[调用queryVisitFiledBizCodeAndValueCode<br/>获取拜访字段+值列表]
    
    G --> I[filedBizCodeList]
    H --> J[filedBizCodeAndValueCodeList]
    
    I --> K[遍历商户下的所有任务]
    J --> K
    
    K --> L[获取当前任务的visitCodes<br/>从taskTempTypeAndVisitBizCodeMap]
    L --> M{visitCodes是否为空?}
    M -->|是| N[跳过当前任务]
    M -->|否| O{拜访字段是否包含所有visitCodes?}
    
    O -->|否| P[记录日志: 当前任务不在拜访结果中]
    O -->|是| Q{actionType是否为CREATE?}
    
    Q -->|是| R[更新taskIndicatorCompletedNum计数<br/>+1]
    Q -->|否| S[检查任务完成条件]
    R --> S
    
    S --> T{任务是否在taskTempTypeAndVisitValueCodeMap中?}
    T -->|否| U[仅记录次数，不更新状态]
    T -->|是| V[获取visitResultConfig<br/>从taskTempTypeAndVisitValueCodeMap]
    
    V --> W{拜访字段+值是否包含任何完成条件?}
    W -->|是| X[设置任务状态为FINISH]
    W -->|否| Y[保持当前任务状态]
    
    U --> Z[更新任务实例]
    X --> Z
    Y --> Z
    
    Z --> AA{是否还有更多任务?}
    AA -->|是| K
    AA -->|否| BB[V2流程处理完成]
    
    P --> AA
    N --> AA
    E --> BB
    
    style A fill:#e1f5fe
    style BB fill:#c8e6c9
    style E fill:#ffcdd2
    style X fill:#fff3e0
    style R fill:#f3e5f5
```

##### ******* 任务推荐过期  $\color{#0089FF}{@孟昊(沐寒)}$ 

```mermaid
---
title: 任务推荐过期
config:
  theme: forest
  themeVariables:
    primaryColor: "#ff8c1f"
---
sequenceDiagram
participant odps as ODPS
participant mq as MetaQ
participant operation as amap-sales-operation
participant db as MySQL
participant ha3 as Ha3引擎

odps ->> mq : 发送推荐过期消息，tag=RECOMMEND_EXPIRED

mq -->> operation : 监听消息，获取taskNo
operation ->> db : 查询任务实例
opt 检查扩展中，推荐时间暂无过期
  operation -->> mq : return
end
operation ->> db : 移除推荐标+推荐时间
db -->> ha3 : 触发引擎更新
operation -->> mq : end

```

*   SQL示意
    

```sql
SELECT  task_no
FROM    kbtech.amap_sales_operation_task_intance_xxx
WHERE   ds = MAX_PT('kbtech.amap_sales_operation_task_intance_xxx')
AND     GET_JSON_OBJECT(ext_info,'$.recommend_type') IS NOT NULL
AND     TO_DATE(GET_JSON_OBJECT(ext_info,'$.recommend_expired_time'),'yyyy-mm-dd hh:mi:ss') < TO_DATE('${yyyymmdd}000000','yyyymmddhhmiss')
```

##### ******** 任务执行过期  $\color{#0089FF}{@孟昊(沐寒)}$ 

```mermaid
---
title: 任务过期过期
config:
  theme: forest
  themeVariables:
    primaryColor: "#ff8c1f"
---
sequenceDiagram
participant odps as ODPS
participant mq as MetaQ
participant operation as amap-sales-operation
participant db as MySQL
participant ha3 as Ha3引擎

odps ->> mq : 发送执行过期消息，tag=EXECUTE_EXPIRED

mq -->> operation : 监听消息，获取taskNo
operation ->> db : 查询任务实例
opt 校验gmt_expire暂无过期
  operation -->> mq : return
end
operation ->> db : 更新状态置 EXPIRED，以及gmt_end时间
db -->> ha3 : 触发引擎更新
operation -->> mq : end

```

*   SQL示意
    

```sql
SELECT  task_no
FROM    kbtech.amap_sales_operation_task_intance_xxx
WHERE   ds = MAX_PT('kbtech.amap_sales_operation_task_intance_xxx')
AND     gmt_expired < TO_DATE('${yyyymmdd}000000','yyyymmddhhmiss')
```

#### 4.1.2 作战地图

##### ******* 绩效目标  $\color{#0089FF}{@余浩(萧启)}$ 

###### 数据加工 $\color{#0089FF}{@潘戈强(晏晨)}$  $\color{#0089FF}{@刘涛(籁玟)}$  $\color{#0089FF}{@余浩(萧启)}$ 

```mermaid
graph LR
    direct[直营绩效数据源<br/>（晏晨加工）] --> directTable[直营绩效表]
    channel[渠道绩效数据源<br/>（籁玟加工）] --> channelTable[渠道绩效表]
    staff[人员岗位表]
    directTable --> etl[数据清洗/ETL]
    channelTable --> etl
    staff --> etl
    etl --> wide[统一大宽表（ODPS表）]
    wide --> load[数据湖加载]
    load --> lake[数据湖表（最终输出）<br/>（晏晨产出）]
    
    subgraph 数据处理流程["数据处理流程"]
    directTable[(直营绩效表)] -. 数据清洗/ETL .- etl
    channelTable[(渠道绩效表)] -. 数据清洗/ETL .- etl
    staff[(人员岗位表)] -. 数据清洗/ETL .- etl
    etl -->|字段合并/统一| wide
    wide -->|数据同步| load
    end
    
    subgraph 数据源内容["数据源内容"]
    广告收入 --> direct
    广告收入 --> channel
    年费收入 --> direct
    年费收入 --> channel
    CPS收入 --> direct
    CPS收入 --> channel
    智能体收入 --> direct
    智能体收入 --> channel
    首充率 --> direct
    首充率 --> channel
    留存率 --> direct
    留存率 --> channel
    GMV --> direct
    GMV --> channel
    end
    
    style direct fill:#f9f,stroke:#333,stroke-width:2px
    style channel fill:#f9f,stroke:#333,stroke-width:2px
    style directTable fill:#bbf,stroke:#00f,stroke-width:2px
    style channelTable fill:#bbf,stroke:#00f,stroke-width:2px
    style staff fill:#e6ffe6,stroke:#00f,stroke-width:2px
    style etl fill:#ff7f50,stroke:#cc0,stroke-width:4px
    style wide fill:#aaffaa,stroke:#0f0,stroke-width:2px
    style load fill:#add8e6,stroke:#00f,stroke-width:4px
    style lake fill:#ffdead,stroke:#ff0,stroke-width:4px
    style 广告收入 fill:#f0f8ff,stroke:#333,stroke-width:1px
    style 年费收入 fill:#f0f8ff,stroke:#333,stroke-width:1px
    style CPS收入 fill:#f0f8ff,stroke:#333,stroke-width:1px
    style 智能体收入 fill:#f0f8ff,stroke:#333,stroke-width:1px
    style 首充率 fill:#f0f8ff,stroke:#333,stroke-width:1px
    style 留存率 fill:#f0f8ff,stroke:#333,stroke-width:1px
    style GMV fill:#f0f8ff,stroke:#333,stroke-width:1px
    style 数据处理流程 fill:#f0f8ff,stroke:#333,stroke-width:2px
    style 数据源内容 fill:#fff8dc,stroke:#333,stroke-width:2px
```

###### 部门离线表获取参与计算的人

[https://dw.alibaba-inc.com/dmc/odps-table/odps.kbdw.dim\_gd\_sale\_employee\_bloc/?\_dt\_ac=M7iZ%2BOg8Qmfk%2Bpg552tn6ACZ9%2FXm1End%2FleW0zB9fqBB1SY%2FRgYf1TdxtAPnz01YSdi%2BfQLsxyyYC6NJ&\_dt\_sig=BhSvGZ%2B2o6%2FTGZNi8oeojHFtfAfO%2FGcgrt8490eQ56Q%3D&\_dt\_ts=1756989634](https://dw.alibaba-inc.com/dmc/odps-table/odps.kbdw.dim_gd_sale_employee_bloc/?_dt_ac=M7iZ%2BOg8Qmfk%2Bpg552tn6ACZ9%2FXm1End%2FleW0zB9fqBB1SY%2FRgYf1TdxtAPnz01YSdi%2BfQLsxyyYC6NJ&_dt_sig=BhSvGZ%2B2o6%2FTGZNi8oeojHFtfAfO%2FGcgrt8490eQ56Q%3D&_dt_ts=1756989634)

*   前端通过部门树，传入部门 id 与层级、渠道运维小二 id、服务商 id
    
    *   如果是部门：则通过层级+部门 id，筛选出所有的人
        
    *   如果是渠道运维小二：通过运维小二的阿里工号，得到所有对应的服务商的人
        
    *   如果是服务商 id：则通过服务商 id 选出所有的数据
        
*   示例：![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d53380fcd992bfba7e3f810c03a9b33c60a5f10673d9c2e4d4ee449f47a07cb3188363347c47ae56b1)
    

###### 数据口径 $\color{#0089FF}{@潘戈强(晏晨)}$ 

```json
{
  "os_user_id": "用户ID（示例：123444）",
  "total_income_current": "总收入当前值（与绩效口径一致，=广告收入+年费续签+CPS收入+智能体收入，示例：2,645.34万）",
  "total_income_target": "总收入目标值（示例：1,234.34万）",
  "total_income_yesterday": "总收入昨天值（示例：2,939.27万，基于-10%环比计算）",
  "total_income_week_ago": "总收入一周前值（示例：2,727.16万，基于-3%周环比计算）",
  "total_income_achievement_rate": "总收入目标达成率（当前值/目标值，示例：214.5%）",
  "advertising_income_current": "广告收入当前值（与绩效口径一致，广告现金消耗，示例：1,234.34万）",
  "advertising_income_yesterday": "广告收入昨天值（示例：1,371.49万，基于-10%环比计算）",
  "advertising_income_week_ago": "广告收入一周前值（示例：1,272.52万，基于-3%周环比计算）",
  "annual_fee_income_current": "年费收入当前值（与绩效口径一致，年费续签收入，示例：373.29万）",
  "annual_fee_income_yesterday": "年费收入昨天值（示例：414.77万，基于-10%环比计算）",
  "annual_fee_income_week_ago": "年费收入一周前值（示例：384.84万，基于-3%周环比计算）",
  "cps_income_current": "CPS收入当前值（与绩效口径一致，抽佣收入，示例：522.72万）",
  "cps_income_yesterday": "CPS收入昨天值（示例：581.91万，基于-10%环比计算）",
  "cps_income_week_ago": "CPS收入一周前值（示例：539.92万，基于-3%周环比计算）",
  "ai_income_current": "智能体收入当前值（与绩效口径一致，存量智能体收入，示例：522.72万）",
  "ai_income_yesterday": "智能体收入昨天值（示例：581.91万，基于-10%环比计算）",
  "ai_income_week_ago": "智能体收入一周前值（示例：539.92万，基于-3%周环比计算）",
  "first_charge_rate_numerator_current": "首充率当前分子（14天实充商户数，示例：156）",
  "first_charge_rate_denominator_current": "首充率当前分母（应充商户数，示例：1000）",
  "first_charge_rate_numerator_yesterday": "首充率昨天分子（示例：173）",
  "first_charge_rate_denominator_yesterday": "首充率昨天分母（示例：1000）",
  "first_charge_rate_numerator_week_ago": "首充率一周前分子（示例：161）",
  "first_charge_rate_denominator_week_ago": "首充率一周前分母（示例：1000）",
  "retention_rate_numerator_current": "留存率当前分子（在投门店数，示例：852）",
  "retention_rate_denominator_current": "留存率当前分母（总门店数，示例：1000）",
  "retention_rate_numerator_yesterday": "留存率昨天分子（示例：947）",
  "retention_rate_denominator_yesterday": "留存率昨天分母（示例：1000）",
  "retention_rate_numerator_week_ago": "留存率一周前分子（示例：878）",
  "retention_rate_denominator_week_ago": "留存率一周前分母（示例：1000）",
  "gmv_current": "GMV当前值（与绩效口径一致，核销GMV，除低频SKA、低频KA外全部展示，示例：234.34万）",
  "gmv_yesterday": "GMV昨天值（示例：260.38万，基于-10%环比计算）",
  "gmv_week_ago": "GMV一周前值（示例：241.59万，基于-3%周环比计算）"
}
```

###### 绩效目标查询时序图（管理者+小二）

```plantuml
@startuml "绩效目标查询时序图"

title <color:Red> <size:60> 绩效目标查询时序图

autonumber

actor "管理者/小二" as user #LightBlue

participant "amap-sales-operation_app\napp" as app #LightGreen

participant "amap-sales-operation_domain\ndomain" as domain #LightYellow

participant "amap-sales-operation_infra\ninfra" as infra #LightCoral

participant "alsc-kbt-merchant-admin\n人员岗位服务" as admin #LightPink

participant "kbcommprod\n绩效数据服务" as kbcommprod #LightGray

participant "数据湖系统\nStarRocks" as datalake #LightCyan

database "Tair缓存系统" as tair #LightSalmon

user->app:<color:Red><b>查询绩效目标</b></color>
activate app
note right of user
    #请求参数包含：
    #- 页面来源（管理者页面/小二页面）
    #- 岗位ID（可选）
    #- 小二ID（可选）
    #- 公司ID（可选）
    #- 查询时间范围
end note

==<color:DarkGreen><size:30>1. 参数校验与缓存检查==
app->app:参数校验
note right of app
    #校验页面来源和必要参数：
    #- 管理者页面：岗位ID或公司ID
    #- 小二页面：小二ID
    #- 查询时间范围
end note

app->app:<color:Blue><b>生成缓存Key</b></color>
note right of app
    #计算查询参数MD5值：
    #- 页面来源、岗位/小二/公司ID
    #- 查询时间范围、环境标记
end note
app->app:<color:Orange><b>检查缓存降级开关</b></color>
note right of app
    #检查缓存降级开关状态
end note

alt 缓存降级开启
    note right of app
        #跳过缓存，直接查询
    end note
else 缓存正常
    app->tair:<color:Orange><b>查询缓存数据</b></color>
    activate tair
    note right of tair
        #通过MD5 Key查询缓存
    end note
    
    alt 缓存命中
        tair->app:返回缓存数据
        deactivate tair
        app->user:返回缓存数据
        deactivate app
        note right of app
            #直接返回缓存数据
        end note
    else 缓存未命中
        deactivate tair
        note right of app
            #继续后续查询流程
        end note
    end
end

==<color:DarkGreen><size:30>2. 岗位/人员/服务商信息查询==
app->domain:查询岗位人员信息
activate domain
note right of domain
    #根据页面来源确定查询策略
end note

alt 管理者页面 - 岗位查询
    domain->infra:<color:Green><b>查询岗位下级信息</b></color>
    activate infra
    infra->admin:<color:Green><b>查询岗位下级岗位</b></color>
    activate admin
    note right of admin
        #查询下级岗位列表
    end note
    admin->infra:返回下级岗位列表
    deactivate admin
    infra->domain:返回下级岗位列表
    deactivate infra

else 小二页面 - 服务商查询
    domain->infra:<color:Green><b>查询小二服务商信息</b></color>
    activate infra
    infra->admin:<color:Green><b>查询小二关联服务商</b></color>
    activate admin
    note right of admin
        #查询服务商列表
    end note
    admin->infra:返回服务商列表
    deactivate admin
    infra->domain:返回服务商列表
    deactivate infra

else 已选择服务商
    domain->infra:<color:Green><b>查询单个服务商信息</b></color>
    activate infra
    infra->admin:<color:Green><b>查询服务商详情</b></color>
    activate admin
    note right of admin
        #查询服务商详细信息
    end note
    admin->infra:返回服务商详情
    deactivate admin
    infra->domain:返回服务商详情
    deactivate infra
end
domain->app:返回岗位人员信息
deactivate domain

==<color:DarkGreen><size:30>3. 绩效数据查询==
app->domain:查询绩效数据
activate domain
note right of domain
    #根据岗位/服务商/小二ID查询绩效数据
end note

domain->infra:<color:Blue><b>查询总收入数据</b></color>
activate infra
infra->kbcommprod:<color:Blue><b>查询总收入指标</b></color>
activate kbcommprod
note right of kbcommprod
    #查询总收入数据
end note
kbcommprod->infra:返回总收入数据
deactivate kbcommprod
infra->domain:返回总收入数据
deactivate infra

domain->infra:<color:Blue><b>查询其他指标数据</b></color>
activate infra
infra->datalake:<color:Blue><b>查询其他绩效指标</b></color>
activate datalake
note right of datalake
    #查询7个指标：广告收入、年费收入、CPS收入、
    #智能体收入、首充率、留存率、GMV
end note
datalake->infra:返回其他指标数据
deactivate datalake
infra->domain:返回其他指标数据
deactivate infra
domain->app:返回绩效数据
deactivate domain

==<color:DarkGreen><size:30>4. 数据组装与缓存策略==
domain->domain:<color:Red><b>构建绩效数据结果</b></color>
activate domain
note right of domain
    #组装返回数据：岗位/服务商信息、总收入、其他7个指标
end note

domain->domain:<color:Red><b>判断数据更新状态</b></color>
note right of domain
    #查询昨天数据：检查实时+离线数据完整性
    #查询前天或更早：无需设置全局标记
end note

alt 查询昨天数据
    domain->tair:<color:Red><b>存储全局标记</b></color>
    activate tair
    note right of tair
        #标记今天数据已更新完全
    end note
    tair->domain:存储完成
    deactivate tair
    
    domain->domain:<color:Red><b>执行缓存策略</b></color>
    note right of domain
        #数据已更新完全：缓存到23:59:59过期
        #数据未更新完全：缓存1小时过期
    end note
else 查询前天或更早数据
    domain->domain:<color:Red><b>执行标准缓存策略</b></color>
    note right of domain
        #直接缓存1小时过期
    end note
end

domain->app:返回组装结果
deactivate domain
note right of domain
    #返回完整的绩效数据
end note

app->app:<color:Orange><b>检查缓存降级开关</b></color>
alt 缓存降级开启
    note right of app
        #跳过缓存存储
    end note
else 缓存正常
    app->tair:<color:Orange><b>存储缓存数据</b></color>
    activate tair
    note right of tair
        #使用MD5 Key存储缓存数据
    end note
    tair->app:存储完成
    deactivate tair
end

app->app:返回结果
note right of app
    #返回绩效数据查询结果
end note

app->user:返回绩效数据查询结果
deactivate app

@enduml

```

###### 绩效指标&完成度查询（仅管理者）

```plantuml
@startuml "绩效数据对比查询时序图"

title <color:Red> <size:60> 绩效数据对比查询时序图

autonumber

actor "管理者/小二" as user #LightBlue

participant "amap-sales-operation_app\napp" as app #LightGreen

participant "amap-sales-operation_domain\ndomain" as domain #LightYellow

participant "amap-sales-operation_infra\ninfra" as infra #LightCoral

participant "alsc-kbt-merchant-admin\n人员岗位服务" as admin #LightPink

participant "kbcommprod\n绩效数据服务" as kbcommprod #LightGray

participant "数据湖系统\nStarRocks" as datalake #LightCyan

database "Tair缓存系统" as tair #LightSalmon

user->app:<color:Red><b>查询绩效数据对比</b></color>
activate app
note right of user
    #请求参数包含：
    #- 页面来源（管理者页面/小二页面）
    #- 岗位ID（可选）
    #- 小二ID（可选）
    #- 公司ID（可选）
    #- 查询时间范围
    #- 对比周期字段
    #- 翻页字段（页码、页大小）
    #- 跳转来源（柱状图点击跳转）
    #- 选中指标类型（七个指标中的具体指标）
end note

==<color:DarkGreen><size:30>1. 参数校验与缓存检查==
app->app:参数校验
note right of app
    #校验页面来源和必要参数：
    #- 管理者页面：岗位ID或公司ID
    #- 小二页面：小二ID
    #- 查询时间范围
    #- 对比周期字段
    #- 翻页字段
    #- 跳转来源和选中指标类型
end note

app->app:<color:Blue><b>生成缓存Key</b></color>
note right of app
    #计算查询参数MD5值：
    #- 页面来源、岗位/小二/公司ID
    #- 查询时间范围、对比周期字段
    #- 翻页字段、跳转来源、选中指标类型
    #- 环境标记
end note
app->app:<color:Orange><b>检查缓存降级开关</b></color>
note right of app
    #检查缓存降级开关状态
end note

alt 缓存降级开启
    note right of app
        #跳过缓存，直接查询
    end note
else 缓存正常
    app->tair:<color:Orange><b>查询缓存数据</b></color>
    activate tair
    note right of tair
        #通过MD5 Key查询缓存
    end note
    
    alt 缓存命中
        tair->app:返回缓存数据
        deactivate tair
        app->user:返回缓存数据
        deactivate app
        note right of app
            #直接返回缓存数据
        end note
    else 缓存未命中
        deactivate tair
        note right of app
            #继续后续查询流程
        end note
    end
end

==<color:DarkGreen><size:30>2. 岗位/人员/服务商信息查询==
app->domain:查询岗位人员信息
activate domain
note right of domain
    #根据页面来源确定查询策略
end note

alt 管理者页面 - 岗位查询
    domain->infra:<color:Green><b>查询岗位下级信息</b></color>
    activate infra
    infra->admin:<color:Green><b>查询岗位下级岗位</b></color>
    activate admin
    note right of admin
        #查询下级岗位列表
    end note
    admin->infra:返回下级岗位列表
    deactivate admin
    infra->domain:返回下级岗位列表
    deactivate infra

else 小二页面 - 服务商查询
    domain->infra:<color:Green><b>查询小二服务商信息</b></color>
    activate infra
    infra->admin:<color:Green><b>查询小二关联服务商</b></color>
    activate admin
    note right of admin
        #查询服务商列表
    end note
    admin->infra:返回服务商列表
    deactivate admin
    infra->domain:返回服务商列表
    deactivate infra

else 已选择服务商
    domain->infra:<color:Green><b>查询单个服务商信息</b></color>
    activate infra
    infra->admin:<color:Green><b>查询服务商详情</b></color>
    activate admin
    note right of admin
        #查询服务商详细信息
    end note
    admin->infra:返回服务商详情
    deactivate admin
    infra->domain:返回服务商详情
    deactivate infra
end
domain->app:返回岗位人员信息
deactivate domain

==<color:DarkGreen><size:30>3. 绩效数据查询==
app->domain:查询绩效数据
activate domain
note right of domain
    #根据岗位/服务商/小二ID查询绩效数据
end note

domain->infra:<color:Blue><b>查询绩效指标数据</b></color>
activate infra
infra->datalake:<color:Blue><b>查询绩效指标</b></color>
activate datalake
note right of datalake
    #查询7个指标：广告收入、年费收入、CPS收入、
    #智能体收入、首充率、留存率、GMV
    #直接查询小二维度，无聚合逻辑
end note
datalake->infra:返回绩效指标数据
deactivate datalake
infra->domain:返回绩效指标数据
deactivate infra
domain->app:返回绩效数据
deactivate domain

==<color:DarkGreen><size:30>4. 数据组装与缓存策略==
domain->domain:<color:Red><b>构建绩效数据结果</b></color>
activate domain
note right of domain
    #组装返回数据：岗位/服务商信息、7个绩效指标
    #处理对比周期数据
    #组装分页信息
end note

domain->domain:<color:Red><b>判断数据更新状态</b></color>
note right of domain
    #查询昨天数据：检查实时+离线数据完整性
    #查询前天或更早：无需设置全局标记
end note

alt 查询昨天数据
    domain->tair:<color:Red><b>存储全局标记</b></color>
    activate tair
    note right of tair
        #标记今天数据已更新完全
    end note
    tair->domain:存储完成
    deactivate tair
    
    domain->domain:<color:Red><b>执行缓存策略</b></color>
    note right of domain
        #数据已更新完全：缓存到23:59:59过期
        #数据未更新完全：缓存1小时过期
    end note
else 查询前天或更早数据
    domain->domain:<color:Red><b>执行标准缓存策略</b></color>
    note right of domain
        #直接缓存1小时过期
    end note
end

domain->app:返回组装结果
deactivate domain
note right of domain
    #返回完整的绩效数据对比结果
    #包含分页信息
end note

app->app:<color:Orange><b>检查缓存降级开关</b></color>
alt 缓存降级开启
    note right of app
        #跳过缓存存储
    end note
else 缓存正常
    app->tair:<color:Orange><b>存储缓存数据</b></color>
    activate tair
    note right of tair
        #使用MD5 Key存储缓存数据
    end note
    tair->app:存储完成
    deactivate tair
end

app->app:返回结果
note right of app
    #返回绩效数据对比查询结果
    #包含分页信息
end note

app->user:返回绩效数据对比查询结果
deactivate app

@enduml

```

##### 4.1.2.2 门店仪表盘  $\color{#0089FF}{@徐文洋(亦竟)}$ 

**1 数据加工【** $\color{#0089FF}{@李亮(秃贝)}$ **】**

[请至钉钉文档查看「白板」](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mfarx5a7pt7x48o554q&rnd=0.0019112956181871477)

**2 数据查询**

[请至钉钉文档查看「白板」](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mf8131212llojoe43uo&rnd=0.0019112956181871477)

**3 数据查询指标【TODO】**

**查询入参：**

| **字段** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| jobId | String | 岗位id |  |
| aliCode | String | 小二id |  |
| companyId | String | 服务商id |  |
| queryScene | String | 查询数据类型 |  |
|  |  |  |  |
|  |  |  |  |

**返回数据【TODO】**

| **字段** | **字段** | **说明** | **示例** |
| --- | --- | --- | --- |
| shopNum【NewStage、BarrierStage、GrowthStage、MatureStage】 | 门店数【新手、瓶颈、成长、成熟期】 | 门店分层，各层级门店数量 |  |
| shopNumRatio | 门店数占比【新手、瓶颈、成长、成熟期】 | 门店分层，各层级门店数量占比 |  |
| shopNumDayRatio | 门店数日环比【新手、瓶颈、成长、成熟期】 |  |  |
| shopNumWeekRatio | 门店数周环比【新手、瓶颈、成长、成熟期】 |  |  |
| shopNumMonthRatio | 门店数月环比【新手、瓶颈、成长、成熟期】 |  |  |
|  |  |  |  |
| shopNumRatioDayRatio | 门店数占比日环比【新手、瓶颈、成长、成熟期】 |  |  |
| shopNumRatioWeekRatio | 门店数占比周环比【新手、瓶颈、成长、成熟期】 |  |  |
| shopNumRatioMonthRatio | 门店数占比月环比【新手、瓶颈、成长、成熟期】 |  |  |
|  |  |  |  |
| newStageStayDaysDistribution | 门店新签阶段时长分布Map<Key,Value> | {"LESS\_THREE\_DAYS":"30.5%","THREE\_TO\_FOURTEEN\_DAYS":"50.5%","OVER\_FOURTEEN\_DAYS":"19.0%",} |  |
|  |  |  |  |
| barrierStageVoucherGrowthDistribution | 瓶颈期门店凭证日均增长分布Map<Key,Value> | {"LESS\_THREE\_DAYS":"30.5%","THREE\_TO\_FOURTEEN\_DAYS":"50.5%","OVER\_FOURTEEN\_DAYS":"19.0%",} |  |
|  |  |  |  |
| growthStageVoucherGrowthDistribution | 成长期门店凭证日均增长分布Map<Key,Value> | {"LESS\_THREE\_DAYS":"30.5%","THREE\_TO\_FOURTEEN\_DAYS":"50.5%","OVER\_FOURTEEN\_DAYS":"19.0%",} |  |
|  |  |  |  |
| matureStageARPUGrowthDistribution | 成熟期门店ARPU日均增长分布Map<Key,Value> | {"LESS\_THREE\_DAYS":"30.5%","THREE\_TO\_FOURTEEN\_DAYS":"50.5%","OVER\_FOURTEEN\_DAYS":"19.0%",} |  |
|  |  |  |  |

##### ******* 分阶段诊断  $\color{#0089FF}{@吴福旺(烦无)}$ 

###### *******.1 新手期分阶段诊断数据

流程图

```plantuml
@startuml 分阶段诊断

title <color:Red> <size:60> 新手期分阶段诊断数据

participant "客户端" as client
participant "主服务端" as server
participant "人员架构表" as org_structure
participant "人店关系" as relation
participant "离线任务表" as task
participant "门店域" as store
participant "缓存" as cache

client -> server: 发起请求（部门id、小二id、服务商id、类型、阶段）
server -> cache: 获取缓存数据
cache -> server: 返回缓存数据
alt 有缓存数据
    server -> client: 返回结果
end
alt 部门/服务商
    server -> org_structure: 根据部门/服务商id获取小二列表
    org_structure -> server: 返回小二列表
end
server -> relation: 根据小二id列表获取人店关系
relation -> server: 返回人店关系
server -> server: 根据人店关系获取门店id列表
server -> store: 通过门店id列表，获取这些门店当前、上周、上月数据
store -> server: 返回门店当前、上周、上月数据
server -> server: 计算新手期门店数、新手期门店占比、月环比、周环比
server -> server: 计算所有门店在新手期停留时间
alt 直营为末级团队或渠道为末级和次末级
    server -> server: 生成催办列表，取新手期停留时间超过3天门店数top3的小二/服务商
end
server -> server: 计算每个小二在新手期停留超过3天的门店的门店数量
alt 子部门
    server -> server: 按子部门聚合新手期停留超过3天的门店的门店数量
end
server -> task: 通过小二id列表查询待办任务
task -> server: 返回待办任务列表
server -> server: 按小二/子部门聚合新手期待办任务，取top3
server -> server: 生成建议文案
note right of server
重点关注xxx团队/xx小二有xx个门店新手期停留时长超过3天
（根据门店数量top3团队/小二），重点做工xx任务、xxx任务
（新进门店诊断任务总量top3）
end note
alt 筛选【新手期停留时长门店数】
    server -> server: 计算小二/子部门新手期停留时长达到筛选条件的门店数量
end
alt 筛选【有五星装修任务的门店数】
    server -> server: 计算小二/子部门有五星装修任务的门店数
end
alt 筛选【有五星装修任务的门店数】
    server -> server: 计算小二/子部门有五星装修任务的门店数
end
alt 筛选【有货架无品任务的门店数】
    server -> server: 计算小二/子部门有货架无品任务的门店数
end
server -> server: 组装柱状图数据
server -> server: 判断是否展示催办按钮
server -> server: 组装数据
server -> client: 返回最终结果

@enduml

```

新增HSF：com.amap.sales.operation.client.OptPhasedDiagnosticsFacade.queryPhasedDiagnosticsData

请求参数

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| jobIds | List<String> | 岗位id列表 |  |
| jobLevel | Integer | 岗位层级，与岗位id列表共存，同时为空，或者同时非空，1～7 |  |
| channelOptStaffIds | List<String> | 渠道运维id列表 |  |
| channelCompanyIds | List<String> | 渠道公司id列表 |  |
| staffIds | List<String> | 运维小二id列表 |  |
| entityType | String | 实体类型（DEPARTMENT-部门，CHANNEL\_OPT-渠道运维，COM-服务商公司，STAFF-小二） |  |
| dataType | String | 查询数据类型，枚举值，（NEWBIE\_SHOP）未出新手期门店停留时长门店数，（FIVE\_STAR\_SHELF\_SHOP）有五星装修任务的门店数，（SHELF\_HAS\_PRODUCTS\_SHOP）有货架无品任务的门店数 |  |
| stayTimeRange | String | 停留时长筛选条件，枚举值，(LESS\_THAN\_THREE)停留0~3天、(FOUR\_TO\_FOURTEEN)停留4~14天、(MORE\_THAN\_FOURTEEN)停留14天以上 |  |
| stage | String | 阶段，枚举，(NEWVBIE)新手期 |  |
| sortType | String | 排序，枚举，（ASC）正序，（DESC）倒序，默认倒序 |  |

返回参数

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| ~~numOfShop~~ | ~~Integer~~ | ~~当前阶段门店数~~ |  |
| ~~ratio~~ | ~~Number~~ | ~~当前阶段门店数占比~~ |  |
| ~~MonthOnMonth~~ | ~~Number~~ | ~~月环比~~ |  |
| ~~weekOnWeek~~ | ~~Number~~ | ~~周环比~~ |  |
| suggest | String | 诊断建议文案 |  |
| remindList | List<RemindDTO> | 催办列表 |  |
| remindList.salesId | String | 小二/服务商id |  |
| remindList.suggest | String | 催办文案 |  |
| remindFlag | boolean | 是否需要展示去催办按钮 |  |
| data | List<StageDataDTO> | 柱状图数据 |  |
| data.sellerId | String | 小二/服务商id |  |
| data.sellerName | String | 小二/服务商名称 |  |
| data.storeDataList | List<StageStoreDataDTO> | 门店数据 |  |
| data.storeDataList.numOfStore | Integer | 门店数 |  |
| data.storeDataList.displayColor | String | 展示颜色 |  |

###### *******.2 瓶颈期分阶段诊断数据

流程图

```plantuml
@startuml 分阶段诊断

title <color:Red> <size:60> 瓶颈期分阶段诊断数据

participant "客户端" as client
participant "主服务端" as server
participant "人员架构表" as org_structure
participant "人店关系" as relation
participant "离线任务表" as task
participant "门店域" as store
participant "缓存" as cache

client -> server: 发起请求（部门id、小二id、服务商id、类型、阶段）
server -> cache: 获取缓存数据
cache -> server: 返回缓存数据
alt 有缓存数据
    server -> client: 返回结果
end
alt 部门/服务商
    server -> org_structure: 根据部门/服务商id获取小二列表
    org_structure -> server: 返回小二列表
end
server -> relation: 根据小二id列表获取人店关系
relation -> server: 返回人店关系
server -> server: 根据人店关系获取门店id列表
server -> store: 通过门店id列表，获取这些门店当前、上周、上月数据
store -> server: 返回门店当前、上周、上月数据
server -> server: 计算瓶颈期门店数、瓶颈期门店占比、月环比、周环比
server -> server: 计算所有门店日均凭证量的变化（近七天比上7天）
server -> server: 计算每个小二/子团队日均凭证量下降幅度超过5%的门店数量
alt 直营为末级团队或渠道为末级和次末级
server -> server: 生成催办列表，取日均凭证量下降幅度超过5%门店数top3的小二/子部门
end
server -> task: 通过门店id列表查询待办任务
task -> server: 返回待办任务列表
server -> server: 计算瓶颈期top3待办任务
server -> server: 生成建议文案
note right of server
重点关注xxx团队/xx小二，xx个门店日均凭证量连续7天较低
（日均凭证量近7天对比上个7天下降/未变化，下降门店数量top3小二），
重点做工xxxx任务（重点关注门店中，诊断得出数量最多的3类任务）
end note
server -> server: 计算每个小二/子团队瓶颈期门店数的门店数量
server -> server: 组装柱状图数据
server -> server: 判断是否展示催办按钮
server -> server: 组装数据
server -> client: 返回最终结果

@enduml
```

新增HSF：com.amap.sales.operation.client.OptPhasedDiagnosticsFacade.queryPhasedDiagnosticsData

请求参数

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| jobIds | List<String> | 岗位id列表 |  |
| jobLevel | Integer | 岗位层级，与岗位id列表共存，同时为空，或者同时非空，1～7 |  |
| channelOptStaffIds | List<String> | 渠道运维id列表 |  |
| channelCompanyIds | List<String> | 渠道公司id列表 |  |
| staffIds | List<String> | 运维小二id列表 |  |
| entityType | String | 实体类型（DEPARTMENT-部门，CHANNEL\_OPT-渠道运维，COM-服务商公司，STAFF-小二） |  |
| dataType | String | 查询数据类型  枚举（NUM\_OF\_SHOP）门店数 |  |
| stage | String | 阶段，枚举，(BOTTLENECK)瓶颈期 |  |
| sortType | String | 排序，枚举，（ASC）正序，（DESC）倒序，默认倒序 |  |

返回参数

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| ~~numOfShop~~ | ~~Integer~~ | ~~当前阶段门店数~~ |  |
| ~~ratio~~ | ~~Number~~ | ~~当前阶段门店数占比~~ |  |
| ~~MonthOnMonth~~ | ~~Number~~ | ~~月环比~~ |  |
| ~~weekOnWeek~~ | ~~Number~~ | ~~周环比~~ |  |
| suggest | String | 诊断建议文案 |  |
| remindList | List<RemindDTO> | 催办列表 |  |
| remindList.salesId | String | 小二/服务商id |  |
| remindList.suggest | String | 催办文案 |  |
| remindFlag | boolean | 是否需要展示去催办按钮 |  |
| data | List<StageDataDTO> | 柱状图数据 |  |
| data.salersId | String | 小二/服务商id |  |
| data.salersName | String | 小二/服务商名称 |  |
| data.storeDataList | List<StageStoreDataDTO> | 门店数据 |  |
| data.storeDataList.numOfStore | Integer | 门店数 |  |
| data.storeDataList.displayColor | String | 展示颜色 |  |

###### *******.3 成长期分阶段诊断数据

流程图

```plantuml
@startuml 分阶段诊断

title <color:Red> <size:60> 成长期分阶段诊断数据

participant "客户端" as client
participant "主服务端" as server
participant "人员架构表" as org_structure
participant "人店关系" as relation
participant "离线任务表" as task
participant "门店域" as store
participant "缓存" as cache

client -> server: 发起请求（部门id、小二id、服务商id、类型、阶段）
server -> cache: 获取缓存数据
cache -> server: 返回缓存数据
alt 有缓存数据
    server -> client: 返回结果
end
alt 部门/服务商
server -> org_structure: 根据部门/服务商id获取小二列表
org_structure -> server: 返回小二列表
end
server -> relation: 根据小二id列表获取人店关系
relation -> server: 返回人店关系
server -> server: 根据人店关系获取门店id列表
server -> store: 通过门店id列表，获取这些门店当前、上周、上月数据
store -> server: 返回门店当前、上周、上月数据
server -> server: 计算成长期门店数、成长期门店占比、月环比、周环比
server -> server: 计算所有门店日均凭证量的变化（近七天比上7天）
server -> server: 计算每个小二/子团队/渠道商日均凭证量下降幅度超过5%的门店数量
alt 直营为末级团队或渠道为末级和次末级
server -> server: 生成催办列表，取日均凭证量下降幅度超过5%门店数top3的小二/子部门
end
server -> task: 通过门店id列表查询待办任务
task -> server: 返回待办任务列表
server -> server: 计算成长期top3待办任务
server -> server: 生成建议文案
note right of server
重点关注xxx团队/xx小二，xx个门店日均凭证量连续7天较低
（日均凭证量近7天对比上个7天下降/未变化，下降门店数量top3小二），
重点做工xxxx任务（重点关注门店中，诊断得出数量最多的3类任务）
end note
server -> server: 计算每个小二/子团队成长期门店数的门店数量
server -> server: 组装柱状图数据
server -> server: 判断是否展示催办按钮
server -> server: 组装数据
server -> client: 返回最终结果

@enduml
```

新增HSF：com.amap.sales.operation.client.OptPhasedDiagnosticsFacade.queryPhasedDiagnosticsData

请求参数

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| jobIds | List<String> | 岗位id列表 |  |
| jobLevel | Integer | 岗位层级，与岗位id列表共存，同时为空，或者同时非空，1～7 |  |
| channelOptStaffIds | List<String> | 渠道运维id列表 |  |
| channelCompanyIds | List<String> | 渠道公司id列表 |  |
| staffIds | List<String> | 运维小二id列表 |  |
| entityType | String | 实体类型（DEPARTMENT-部门，CHANNEL\_OPT-渠道运维，COM-服务商公司，STAFF-小二） |  |
| dataType | String | 查询数据类型 枚举，（NUM\_OF\_SHOP）门店数 |  |
| stage | String | 阶段，枚举，(DEVELOP)成长期 |  |
| sortType | String | 排序，枚举，（ASC）正序，（DESC）倒序，默认倒序 |  |

返回参数

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| ~~numOfShop~~ | ~~Integer~~ | ~~当前阶段门店数~~ |  |
| ~~ratio~~ | ~~Number~~ | ~~当前阶段门店数占比~~ |  |
| ~~MonthOnMonth~~ | ~~Number~~ | ~~月环比~~ |  |
| ~~weekOnWeek~~ | ~~Number~~ | ~~周环比~~ |  |
| suggest | String | 诊断建议文案 |  |
| remindList | List<RemindDTO> | 催办列表 |  |
| remindList.salesId | String | 小二/子团队/服务商id |  |
| remindList.suggest | String | 催办文案 |  |
| remindFlag | boolean | 是否需要展示去催办按钮 |  |
| data | List<StageDataDTO> | 柱状图数据 |  |
| data.salersId | String | 小二/服务商id |  |
| data.salersName | String | 小二/服务商名称 |  |
| data.storeDataList | List<StageStoreDataDTO> | 门店数据 |  |
| data.storeDataList.numOfStore | Integer | 门店数 |  |
| data.storeDataList.displayColor | String | 展示颜色 |  |

###### *******.4 成熟期分阶段诊断数据

流程图

```plantuml
@startuml 分阶段诊断

title <color:Red> <size:60> 成熟期分阶段诊断数据

participant "客户端" as client
participant "主服务端" as server
participant "人员架构表" as org_structure
participant "人店关系" as relation
participant "离线任务表" as task
participant "门店域" as store
participant "缓存" as cache

client -> server: 发起请求（部门id、小二id、服务商id、类型、阶段）
server -> cache: 获取缓存数据
cache -> server: 返回缓存数据
alt 有缓存数据
    server -> client: 返回结果
end
alt 部门/服务商
server -> org_structure: 根据部门/服务商id获取小二列表
org_structure -> server: 返回小二列表
end
server -> relation: 根据小二id列表获取人店关系
relation -> server: 返回人店关系
server -> server: 根据人店关系获取门店id列表
server -> store: 通过门店id列表，获取这些门店当前、上周、上月数据
store -> server: 返回门店当前、上周、上月数据
server -> server: 计算成熟期门店数、成熟期门店占比、月环比、周环比
server -> server: 计算所有门店日均店均ARPU的变化（近七天比上7天）
server -> server: 计算每个小二/子团队/渠道商日均店均ARPU下降幅度超过5%的门店数量
alt 直营为末级团队或渠道为末级和次末级
server -> server: 生成催办列表，取日日均店均ARPU下降幅度超过5%门店数top3的小二/子部门
end
server -> task: 通过门店id列表查询待办任务
task -> server: 返回待办任务列表
server -> server: 计算成熟期top3待办任务
server -> server: 生成建议文案
note right of server
重点关注xxx团队/xx小二，xx个门店日均店均ARPU下降幅度超过5%
（店均ARPU近7天对比上个7天下降/未变化，下降门店数量top3小二），
重点做工xxxx任务（重点关注门店中，诊断得出数量最多的3类任务）
end note
server -> server: 计算每个小二/子团队成熟期门店数的门店数量
server -> server: 组装柱状图数据
server -> server: 判断是否展示催办按钮
server -> server: 组装数据
server -> client: 返回最终结果

@enduml
```

新增HSF：com.amap.sales.operation.client.OptPhasedDiagnosticsFacade.queryPhasedDiagnosticsData

请求参数

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| jobIds | List<String> | 岗位id列表 |  |
| jobLevel | Integer | 岗位层级，与岗位id列表共存，同时为空，或者同时非空，1～7 |  |
| channelOptStaffIds | List<String> | 渠道运维id列表 |  |
| channelCompanyIds | List<String> | 渠道公司id列表 |  |
| staffIds | List<String> | 运维小二id列表 |  |
| entityType | String | 实体类型（DEPARTMENT-部门，CHANNEL\_OPT-渠道运维，COM-服务商公司，STAFF-小二） |  |
| dataType | String | 查询数据类型 枚举（NUM\_OF\_SHOP）门店数，（ARPU\_WEEK\_GROWTH）日均店均ARPU周增幅 |  |
| stage | String | 阶段，枚举，MATURE |  |
| sortType | String | 排序，枚举，（ASC）正序，（DESC）倒序，默认倒序 |  |

返回参数

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| ~~numOfShop~~ | ~~Integer~~ | ~~当前阶段门店数~~ |  |
| ~~ratio~~ | ~~Number~~ | ~~当前阶段门店数占比~~ |  |
| ~~MonthOnMonth~~ | ~~Number~~ | ~~月环比~~ |  |
| ~~weekOnWeek~~ | ~~Number~~ | ~~周环比~~ |  |
| suggest | String | 诊断建议文案 |  |
| remindList | List<RemindDTO> | 催办列表 |  |
| remindList.salesId | String | 小二/服务商id |  |
| remindList.suggest | String | 催办文案 |  |
| remindFlag | boolean | 是否需要展示去催办按钮 |  |
| data | List<StageDataDTO> | 柱状图数据 |  |
| data.salersId | String | 小二/服务商id |  |
| data.salersName | String | 小二/服务商名称 |  |
| data.storeDataList | List<StageStoreDataDTO> | 门店数据 |  |
| data.storeDataList.numOfStore | Integer | 门店数 |  |
| data.storeDataList.displayColor | String | 展示颜色 |  |

###### *******.5 柱状图去催办

流程图

```plantuml
@startuml 分阶段诊断

title 去催办文案获取流程图

participant "客户端" as client
participant "主服务端" as server
participant "离线任务表" as task

client -> server: 发起请求（小二/渠道商/服务商id，阶段）
server -> task: 通过小二/渠道商/服务商id，查询当前阶段催办任务
task -> server: 返回催办任务列表
server -> server: 判断该小二/渠道商/服务商任务是否达到上限
alt 小二任务未达上限
server -> server: 计算top3任务
server -> server: 生成催办文案
else 小二任务已达上限
server -> server: 生成超限文案
end
server -> client: 返回最终结果
@enduml
```

新增HSF接口：com.amap.sales.operation.client.OptPhasedDiagnosticsFacade.followUp

请求参数

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| employeeNo | String | 小二id |  |

返回参数

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| isOverLimit | boolean | 是否超限 |  |
| suggest | String | 催办文案 |  |

##### ******* 任务汇总明细  $\color{#0089FF}{@吴福旺(烦无)}$ 

###### *******.1 任务汇总

流程图

```plantuml
@startuml 分阶段诊断

title 诊断建议流程图

participant "客户端" as client
participant "主服务端" as server
participant "离线任务表" as task

client -> server: 发起请求（团队/商户id）
server -> task: 根据部门id，小二id，服务商id获取任务列表
task -> server: 返回任务列表
server -> server: 筛选符合条件的任务生成汇总的任务列表
server -> client: 返回最终结果

@enduml
```

新增HSF接口：com.amap.sales.operation.client.OptTaskSummaryFacade.queryTaskSummary

请求参数

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| jobIds | List<String> | 岗位id列表 |  |
| jobLevel | Integer | 岗位层级，与岗位id列表共存，同时为空，或者同时非空，1～7 |  |
| channelOptStaffIds | List<String> | 渠道运维id列表 |  |
| channelCompanyIds | List<String> | 渠道公司id列表 |  |
| entityType | String | 实体类型（DEPARTMENT-部门，CHANNEL\_OPT-渠道运维，COM-服务商公司） |  |
| taskPublishDate | String | 任务下发日期 YYYY-MM-DD |  |
| taskFollowUpDate | String | 任务催办日期 YYYY-MM-DD |  |
| taskType | String | 任务类型，枚举code，参考4.1.1任务枚举 |  |
| taskSource | String | 任务来源 |  |

返回参数(OptTaskSummaryDataDTO)

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| displayName | String | 展示名称 部门名称或小二名称或服务商名称 |  |
| numOfTask | String | 任务下发量 |  |
| numOfFinishTask | String | 任务完成量 |  |
| ratioOfFinishTask | String | 任务完成率 |  |
| numOfFinishTaskObject | String | 任务目标完成量 |  |
| ratioOfFinishTaskObject | String | 任务目标完成率 |  |
| numOfFollowUpDueTask | String | 主管催办量 |  |
| numOfFinishFollowUpDueTask | String | 主管催办完成量 |  |
| ratioOfFinishFollowUpDueTask | String | 主管催办完成率 |  |
| numOfRecTask | String | 系统推荐量 |  |
| numOfFinishRecTask | String | 系统推荐完成量 |  |
| ratioOfFinishRecTask | String | 系统推荐完成率 |  |
| timeRangeOfTask | String | 任务平均完成时效 |  |
| timeRangeOfTaskObject | String | 任务目标平均完成时效 |  |

返回示例

###### *******.2 任务明细

流程图

```plantuml
@startuml 分阶段诊断

title 诊断建议流程图

participant "客户端" as client
participant "主服务端" as server
participant "离线任务表" as task

client -> server: 发起请求（任务id）
server -> task: 根据任务id查询任务详情
task -> server: 返回任务详情
server -> server: 组装任务明细
server -> client: 返回最终结果

@enduml
```

新增HSF接口：com.amap.sales.operation.client.OptTaskSummaryFacade.queryTaskDetail

请求参数

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| jobIds | List<String> | 岗位id列表 |  |
| jobLevel | Integer | 岗位层级，与岗位id列表共存，同时为空，或者同时非空，1～7 |  |
| channelOptStaffIds | List<String> | 渠道运维id列表 |  |
| channelCompanyIds | List<String> | 渠道公司id列表 |  |
| entityType | String | 实体类型（DEPARTMENT-部门，CHANNEL\_OPT-渠道运维，COM-服务商公司） |  |
| taskType | String | 任务名称 |  |
| taskPublishDate | Date | 任务下发日期 |  |
| taskFollowUpDueDate | Date | 催办日期 |  |
| taskSource | String | 任务来源 |  |

返回参数

| **标识符** | **类型** | **说明** | **示例** |
| --- | --- | --- | --- |
| taskName | String | 任务名称 |  |
| taskStatus | String | 任务状态：待处理、已完成、未完成 |  |
| taskObjectStatus | String | 任务目标：已完成、未完成 |  |
| hasFollowUpDue | boolean | 是否催办：是、否 |  |
| taskSource | String | 任务来源：系统下发、主管下达 |  |
| merchantName | String | 商户名称 |  |
| merchantId | String | 商户ID |  |
| shopName | String | 门店名称 |  |
| shopId | String | 门店ID |  |
| employeeName | String | 运维小二 |  |
| job3Name | String | 三级部门：如果直营则展示 |  |
| job4Name | String | 四级部门：如果直营则展示 |  |
| companyName | String | 服务商：如果渠道则展示 |  |
| taskPublishDate | Date | 任务下发时间 |  |
| taskFinishDate | Date | 任务完成时间 |  |
| timeRangeOfTask | Number | 任务完成时效（下发-完成） |  |
| taskObjectFinishDate | Date | 目标完成时间 |  |

#### 4.1.3 小二工作台

##### 4.1.3.1 工作概览  $\color{#0089FF}{@余浩(萧启)}$ 

###### 诊断建议

![image.png](/core/api/resources/img/5eecdaf48460cde5d62d9a19e3ce8e7270a144f4a7cc37d775b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d5002859039ac980e619cfe8a5e9d3599060455f2a65f18e899e307901d6dbb7a9baa66e12857fdf3b)

说明：诊断建议更多是做商户维度的任务统计，因此 1 个商户有>=1 个今日推荐的任务，既需要给该商户打上**今日推荐**标记

```plantuml
@startuml "运维诊断建议生成规则时序图"

title <color:Red> <size:60> 运维诊断建议生成规则时序图

autonumber

actor "运维人员" as user #LightBlue

participant "amap-sales-operation_app\napp" as app #LightGreen

participant "amap-sales-operation_domain\ndomain" as domain #LightYellow

participant "amap-sales-operation_infra\ninfra" as infra #LightCoral

database "HA3引擎" as ha3 #LightPink

database "MySQL数据库" as mysql #LightGray

user->app:<color:Red><b>请求生成诊断建议</b></color>
activate app

==<color:DarkGreen><size:30>1. 参数校验==
app->app:参数校验
note right of app
    #校验操作人信息
    #校验请求参数
end note

==<color:DarkGreen><size:30>2. 查询待办任务商户==
app->domain:查询待办任务商户
activate domain
domain->infra:调用HA3引擎查询
activate infra
infra->ha3:通过待办任务标记查询商户
activate ha3
ha3-->infra:返回商户ID列表
deactivate ha3
infra-->domain:返回商户ID列表
deactivate infra
domain-->app:返回商户ID列表
deactivate domain

==<color:DarkGreen><size:30>3. 查询商户分层信息==
app->domain:查询商户分层信息
activate domain
domain->infra:批量查询商户信息
activate infra
infra->mysql:查询商户分层数据
activate mysql
mysql-->infra:返回商户分层信息
deactivate mysql
infra-->domain:返回商户分层信息
deactivate infra
domain-->app:返回商户分层信息
deactivate domain

==<color:DarkGreen><size:30>4. 分层统计与文案生成==
app->app:<color:Blue><b>分层统计</b></color>
app->app:<color:Blue><b>组装分层数据</b></color>
app->app:<color:Blue><b>生成策略建议</b></color>

==<color:DarkGreen><size:30>5. 返回结果==
app-->user:返回诊断建议
deactivate app

note over user, app
**示例文案**：
今日您名下共4个商家需完成15个任务，需重点关注，
其中新手期商户1个，需完成五星门店、货架有品任务；
成长期商户2个，需完成年费续充任务，
成熟期商户1个，需完成广告续签任务，
另，您的主管给您的2个是商户催办提醒，请尽快完成。
end note

note over app
**规则说明**：
今日您名下共xx个商家需完成xx个任务，需重点关注，
其中新手期商户xx个，需完成xx任务、xx任务；
瓶颈期商户xx个，需完成xx任务，
成长期商户xx个，需完成xx任务，
成熟期商户xx个，需完成xx任务，
另，您的主管给您的X个是商户催办提醒，请尽快完成。
（分层内商户为0时，则不显示对应分层的文案）
end note

@enduml

```

###### 商家任务饼状图

```plantuml
@startuml "今日商家待办任务饼状图时序图"

title <color:Red> <size:60> 今日商家待办任务饼状图时序图

autonumber

actor "运维人员" as user #LightBlue

participant "amap-sales-operation_app\napp" as app #LightGreen

participant "amap-sales-operation_domain\ndomain" as domain #LightYellow

participant "amap-sales-operation_infra\ninfra" as infra #LightCoral

database "HA3引擎" as ha3 #LightPink

database "MySQL数据库" as mysql #LightGray

user->app:<color:Red><b>请求今日待办任务饼状图</b></color>
activate app

==<color:DarkGreen><size:30>1. 参数校验==
app->app:参数校验
note right of app
    #校验操作人信息
    #校验请求参数
end note

==<color:DarkGreen><size:30>2. 查询待办任务商户==
app->domain:查询待办任务商户
activate domain
domain->infra:调用HA3引擎查询
activate infra
infra->ha3:通过待办任务标记查询商户
activate ha3
ha3-->infra:返回商户ID列表
deactivate ha3
infra-->domain:返回商户ID列表
deactivate infra
domain-->app:返回商户ID列表
deactivate domain

==<color:DarkGreen><size:30>3. 查询商户分层信息==
app->domain:查询商户分层信息
activate domain
domain->infra:批量查询商户信息
activate infra
infra->mysql:查询商户分层数据
activate mysql
mysql-->infra:返回商户分层信息
deactivate mysql
infra-->domain:返回商户分层信息
deactivate infra
domain-->app:返回商户分层信息
deactivate domain

==<color:DarkGreen><size:30>4. 任务数量统计==
app->domain:查询各分层任务数量
activate domain
domain->infra:批量查询任务信息
activate infra
infra->mysql:查询任务统计数据
activate mysql
mysql-->infra:返回任务统计信息
deactivate mysql
infra-->domain:返回任务统计信息
deactivate infra
domain-->app:返回任务统计信息
deactivate domain

==<color:DarkGreen><size:30>5. 饼状图数据组装==
app->app:<color:Blue><b>计算分层占比</b></color>
app->app:<color:Blue><b>组装饼状图数据</b></color>
app->app:<color:Blue><b>生成策略建议</b></color>

==<color:DarkGreen><size:30>6. 返回结果==
app-->user:返回饼状图数据
deactivate app

note over user, app
**饼状图数据结构**：
- 新手期：30.3%（蓝色）
- 成长期：30%（紫色）
- 瓶颈期：24%（黄色）
- 成熟期：10%（青色）
- 其他：5.7%（红色）

**策略建议**：
今日有10个商家效果待优化，快去完提升吧
end note

note over app
**数据组装规则**：
1. 统计各分层商户数量
2. 计算各分层占比
3. 统计各分层任务数量
4. 生成饼状图数据结构
5. 根据统计结果生成策略建议
end note

@enduml

```

##### 4.1.3.2绩效目标  $\color{#0089FF}{@余浩(萧启)}$ 

同管理者工作台

##### 4.1.3.3 门店仪表盘  $\color{#0089FF}{@徐文洋(亦竟)}$ 

##### ******* 今日待沟通商户  $\color{#0089FF}{@唐皓源(榆澄)}$ 

###### *******.1 代沟通商户聚合查询

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d52f272206464bb318f2652c9d434ec2d0f0465584577b91b2504d99edb38c28624757ecb7a5875108)

接口：amap-sales-operation.AgentOperationQueryFacade.queryTodayRecommendTaskInfo

```plantuml
@startuml 今日待建议沟通商户查询流程图

title 今日待建议沟通商户查询流程图

participant "前端调用方" as client
participant "amap-sales-operation\napp层" as app
participant "amap-sales-operation\ndomain层" as domain
participant "amap-sales-operation\ninfra层" as infra
participant "Saro人户引擎" as saro
participant "MySQL数据库\ntask_instance表" as mysql

== 1. 接口调用 ==
client -> app: 调用queryTodayRecommendTaskInfo
note right: 入参：运维小二ID

== 2. 参数校验 ==
app -> app: 校验请求参数
note right: 校验运维小二ID、操作员信息等

alt 参数校验失败
    app -> client: 返回参数错误
else 参数校验成功
    app -> domain: 执行查询业务逻辑
    note right: 传递运维小二ID
end

== 3. 查询Saro引擎 ==
domain -> infra: 构建Saro查询条件
note right: 根据运维小二ID查询recommend_task_ids

infra -> saro: 查询推荐任务信息
note right: 查询条件：operation_staff_id = 运维小二ID

saro -> infra: 返回推荐任务数据
note right: 返回字段：recommend_task_ids, merchant_pid,\nmerchant_name, arpu（根据arpu值排序）

infra -> domain: 返回解析后的数据
note right: 解析recommend_task_ids为task_id列表\n获取商户信息列表

== 4. 查询任务实例 ==
domain -> infra: 查询任务实例数据
note right: 根据商户id和task_id列表查询task_instance表

infra -> mysql: 逐个执行数据库查询
note right: 查询条件：pid = 商户id，task_no IN (task_id列表)，is_deleted = 0

mysql  -> domain: 返回任务实例列表

== 5. 业务逻辑处理 ==
domain -> domain: 根据综合优先级取出最高优任务实例
domain -> domain: 计算任务倒计时 = recommendExpireTime - 当前时间
domain -> domain: 提取推荐标签和任务，类型标签
domain -> domain: 聚合门店和任务数量
note right: 按商户PID聚合：\n- 统计有推荐任务的门店数量\n- 统计推荐任务总数量


== 6. 结果返回 ==
domain -> app: 返回处理结果
note right: 返回商户维度的推荐任务列表

app -> client: 返回最终结果
note right: 返回格式：ResultDTO<List<TodayRecommendTaskInfoDTO>>

@enduml

```

**入参 TodayRecommendTaskInfoRequest**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
| --- | --- | --- | --- | --- |
| employeeId | String | 是 | 运维小二ID | "agent001" |

**出参**

```json
  {
    "result": true,
    "code": "00000",
    "message": null,
    "version": "1.0",
    "timestamp": "1753099894907",
    "success": true,
    "msgInfo": "调用成功",
    "msgCode": "SUCCESS",
    "traceId": "0bfb408b17530998928682094e162f",
    "data": {
        "result": true,
        "traceId": "0bfb408b17530998928682094e162f",
        "code": "1",
        "dataList": {[
              "merchanId":"",
              "merchantName":"",
              "arpu":"",
              "recommendExpiredTime:"",
              "recommendType":"",
              "taskType":"",
              "shopCount":"",
              "taskName":"",
              "incomeValueText":"",
              "exposureIncText":""               
            ]
        },
        "success": true,
        "message": null,
        "msgInfo": "调用成功",
        "version": "1.0",
        "msgCode": "SUCCESS",
        "timestamp": "1753099894900"
    }
}

```

**TodayRecommendTaskInfo****DTO参数表格**

| 参数名 | 类型 | 说明 | 示例值 |
| --- | --- | --- | --- |
| merchanId | String | 商户PID | "2088123456789012345" |
| merchantName | String | 商户名称 | "测试商户A" |
| arpu | BigDecimal | 商户ARPU值 | 1500.50 |
| countdown | Long | 推荐任务倒计时（秒） | 86400 |
| recommendType | String | 推荐标签 | SYSTEM/REMIND/ARRANGE<br>系统推荐/主管催办/主管下发 |
| taskType | String | 任务类型标签 |  |
| shopCount | Integer | 该商户下有推荐任务的门店数量 | 3 |
| taskName | String | 任务名称 |  |
| incomeValue | BigDecimal | 预计收入增长 |  |
| exposureInc | BigDecimal | 预计曝光增长 |  |

##### 4.1.3.5 商户列表  $\color{#0089FF}{@余浩(萧启)}$ 

###### 引擎修改逻辑 

*   TODO 人户引擎，加一个服务商id字段； 列表查询，出的4个字段要切新表
    

```mermaid
graph LR
    %% 数据源
    taskRecommend[(任务推荐来源<br/>（实时MySQL表）)] --> saro[SARO加工]
    shopLayer{{门店分层<br/>（离线表）}} --> saro
    groupInfo[(群信息<br/>（实时MySQL表）)] --> saro
    
    %% SARO处理
    saro --> engine[商户列表引擎]
    
    %% 筛选项
    engine -.-> taskSourceFilter[任务推荐来源筛选<br/>主管下达、主管催办、智能推荐]
    engine -.-> shopLayerFilter[门店分层筛选<br/>新手期、瓶颈期、成长期、成熟期]
    engine -.-> noGroupFilter[仅查看无群商户<br/>（企微需求）]
    
    %% 样式 - MySQL表（数据库图标）
    style taskRecommend fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style groupInfo fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    
    %% 样式 - 离线表（菱形）
    style shopLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    
    %% 样式 - 处理流程
    style saro fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    style engine fill:#fce4ec,stroke:#880e4f,stroke-width:3px
    
    %% 样式 - 筛选项（普通矩形，虚线边框）
    style taskSourceFilter fill:#e1f5fe,stroke:#01579b,stroke-width:1px,stroke-dasharray: 3 3
    style shopLayerFilter fill:#fff3e0,stroke:#e65100,stroke-width:1px,stroke-dasharray: 3 3
    style noGroupFilter fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px,stroke-dasharray: 3 3

```

###### 查询流程

```plantuml
@startuml "商户列表查询时序图"

title <color:Red> <size:60> 商户列表查询时序图

autonumber

actor "用户" as user #LightBlue

participant "app" as app #LightGreen

participant "domain" as domain #LightYellow

participant "infra" as infra #LightCoral

participant "HA3引擎" as ha3 #LightGray

participant "TaskInstanceV2" as task #LightCyan

user->app:查询商户列表
activate app

==<color:DarkGreen><size:30>1. 高优任务查询（核心流程）==
app->domain:查询高优任务
activate domain
domain->infra:查询高优任务数据
activate infra
infra->ha3:查询高优任务引擎
activate ha3
note right of ha3
    #查询高优先级任务
    #获取任务ID列表
end note
ha3->infra:返回任务ID列表
deactivate ha3
infra->task:根据任务ID查询数据库
activate task
note right of task
    #查询TaskInstanceV2表
    #获取任务详细信息
end note
task->infra:返回任务详细信息
deactivate task
infra->domain:返回高优任务数据
deactivate infra
domain->app:返回高优任务数据
deactivate domain

==<color:DarkGreen><size:30>2. 基于高优任务生成其他Tab数据==
app->domain:基于高优任务生成其他数据
activate domain

domain->domain:<color:Purple><b>生成任务状态数据</b></color>
note right of domain
    #基于高优任务数据：
    #- 统计各状态任务数量
    #- 计算完成率、逾期率
end note

domain->domain:<color:Orange><b>生成商户诊断数据</b></color>
note right of domain
    #基于高优任务数据：
    #- 分析任务完成情况
    #- 生成诊断建议文案
end note

domain->domain:<color:Blue><b>生成商户推荐分析数据</b></color>
note right of domain
    #基于高优任务数据：
    #- 分析任务执行效果
    #- 生成推荐分析结果
end note

domain->app:返回四个Tab数据
deactivate domain

app->user:返回商户列表查询结果
deactivate app

@enduml

```

 $\color{#0089FF}{@唐皓源(榆澄)}$ 查询高优任务

##### ******* 商户详情  $\color{#0089FF}{@唐皓源(榆澄)}$ 

###### *******.1 查询门店概览summary

产品形态：

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d5b9fe555ff8277a2f2bbd5acae448f8587bc94289ca5190695ae703950241d0aa34a3e914b9e51695)

```plantuml
@startuml 商户任务详情查询流程图
title 商户任务详情查询流程图

participant "前端调用方" as client
participant "amap-sales-operation\napp层" as app
participant "amap-sales-operation\ndomain层" as domain
participant "amap-sales-operation\ninfra层" as infra
participant "MySQL数据库\ntask_instance表" as mysql

== 1. 查询summary ==
    client -> app: 调用summary查询接口
    note right: 入参：运维小二ID、商户PID
    app -> app: 校验请求参数
alt 参数校验失败
else 参数校验成功
    app -> client: 返回参数错误
    app -> app:根据接口queryTodayMerchantTodoTaskPieChart查询summary任务数据
    app -> 喜报数据源: 查询曝光量、访问量等数据
    喜报数据源 -> app: 数据返回
    app ->client: 包装dto返回
end
@enduml
```

接口：com.amap.sales.operation.client.AgentOperationQueryFacade#queryMerchantTaskSummaryInfo

*   入参 ：MerchantTaskSummaryInfoRequest请求参数 
    

| 参数名 | 参数类型 | 参数示例说明 |
| --- | --- | --- |
| merchantId | String |  |
| ~~employeeId~~ | ~~String~~ |  |

*   出参
    

```json
{
    "result": true,
    "code": "00000",
    "message": null,
    "version": "1.0",
    "timestamp": "1753099894907",
    "success": true,
    "msgInfo": "调用成功",
    "msgCode": "SUCCESS",
    "traceId": "0bfb408b17530998928682094e162f",
    "data": {
        "result": true,
        "traceId": "0bfb408b17530998928682094e162f",
        "code": "1",
        "data": {
            "totalTaskCnt": 25,
            "remindTaskCnt": 4,（这里注意如果数字超过200，或者超过800，前端显示200+或者800+，底层最多只能支持这样）
            "layerDistribution": [
                {
                    "layerName": "新手期",
                    "layerCode": "NEWBIE",
                    "shopCount": 3,
                    "taskCount": 8
                },
                {
                    "layerName": "成长期",
                    "layerCode": "GROWTH",
                    "shopCount": 3,
                    "taskCount": 7
                },
                {
                    "layerName": "瓶颈期",
                    "layerCode": "BOTTLENECK",
                    "shopCount": 2,
                    "taskCount": 6
                },
                {
                    "layerName": "成熟期",
                    "layerCode": "MATURE",
                    "shopCount": 1,
                    "taskCount": 3
                }
            ]
        },
        "success": true,
        "message": null,
        "msgInfo": "调用成功",
        "version": "1.0",
        "msgCode": "SUCCESS",
        "timestamp": "1753099894900"
    }
}

```

###### *******.2 查询数据概览

复用现状：

![image.png](/core/api/resources/img/5eecdaf48460cde50dbf9b218f9d2db75125228667f3c1b275b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d5f45726572ad3346d6425cc0ffb93d3919993eac0148e71133ad8a22f2431f081deef77342442fdf3)

follow之前的接口与产品形态[《\[20250225\]喜报ai分析优化》](https://alidocs.dingtalk.com/i/nodes/GZLxjv9VGwzQ3Ke1H9O5KAdOV6EDybno)

接口：

ai数据分析：com.amap.sales.operation.client.OptAnalysisToolFacade#analysis

分析结果查询：com.amap.sales.operation.client.OptAnalysisToolFacade#queryAnalysisInfo

*********.3 查询商户任务详情 - 不包括内嵌的门店列表**

**产品形态：**

![image.png](/core/api/resources/img/5eecdaf48460cde5f89de59e9fed085b6957f7519871d6e075b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d5754e1022a93861c9dd487db0192b33677ec96a88ab7ff40a140dcd80cb7a9a2b636453bc1f727f91)

时序图

```plantuml
@startuml 商户任务详情查询流程图

title 商户任务详情查询流程图

participant "前端调用方" as client
participant "amap-sales-operation\napp层" as app
participant "amap-sales-operation\ndomain层" as domain
participant "amap-sales-operation\ninfra层" as infra
participant "MySQL数据库\ntask_instance表" as mysql

== 1. 分页查询任务详情 ==
client -> app: 调用queryMerchantTaskDetails
note right: 入参：运维小二ID、商户PID、\n任务状态、任务来源、门店分页信息

app -> app: 校验请求参数
note right: 校验运维小二ID、商户PID、\n操作员信息等必填参数

alt 参数校验失败
    app -> client: 返回参数错误
else 参数校验成功
    app -> domain: 执行查询业务逻辑
    note right: 传递查询条件
end
domain -> domain: 构建任务查询条件
note right: 基础条件：pid = 商户PID \nAND is_deleted = 0\nAnd 状态条件：任务状态 in 入参statusList \nAnd 查询推荐任务还是全部任务 入参taskSource = ALL时查询全部任务，taskSource = RECOMMEND时查询推荐任务\nAnd 任务目标完成状态 in 入参targetStatusList（未完成：INCOMPLETE ，已完成：COMPLETED）
domain -> infra: 查询任务实例数据
note right: 根据查询条件查询task_instance表

infra -> mysql: 执行数据库查询
note right: 查询条件：\n实例V2表pid = 商户id \nAND 实例V2表is_deleted = 0 \nAND 实例V2表status IN statusList \nAND 实例V2表target_status IN  targetStatusList

mysql -> infra: 返回任务实例数据

infra -> domain: 返回任务实例列表
domain -> domain: 过滤任务实例表
note right: ORDER BY 实例V2表中ext拓展字段中priority字段 DESC\ntaskSource为ALL时不过滤，taskSource为RECOMMEND时，过滤条件实例V2表ext拓展字段中recommendTpye不为空的任务实例
domain -> domain: 统计组装各个任务类型task_type对应的门店数量
note right: 按task_type分组统计shop_id不为空的任务实例数量，代表某一类型的任务有多少个门店命中And过滤ext_info中recommendType等于ARRANGE，即主管下发的任务不参与统计门店数，得到shopCount


domain -> domain: 构建MerchantTaskDetailDTO对象
note right: 构建MerchantTaskDetailDTO：\n- MerchantTaskDetailDTO的targetStatus为任务实例表中的target_status字段\n- MerchantTaskDetailDTO的recommendType为任务实例v2表中ext_info中的recommendType\n- MerchantTaskDetailDTO的任务名称taskName，查询TaskTemplateNew任务模版表中的TaskRuleInfoModel的taskName\n- MerchantTaskDetailDTO的shopCount为命中某任务类型且不为主管下发的门店任务数\n- MerchantTaskDetailDTO的suggestCommunication查询TaskTemplateNew任务模版表中的TaskRuleInfoModel的taskName\n-其他变量

domain -> domain: 应用分页逻辑
note right: 根据pageNo和pageSize进行店铺的分页

domain -> app: 返回处理结果
note right: 返回分页的任务详情列表

app -> client: 返回最终结果
note right: 返回格式：ResultDTO<PageDTO<MerchantTaskDetailDTO>>

@enduml

```

接口：amap-sales-operation.AgentOperationQueryFacade.queryMerchantTaskDetail

*   入参 ：MerchantTaskDetailRequest请求参数 
    

| 参数名 | 参数类型 | 参数示例说明 |
| --- | --- | --- |
| merchantId | String |  |
| employeeId | String |  |
| taskStatus | String | 待处理：PROCESSING<br>未完成：INCOMPLETE  <br>已完成：COMPLETED<br>**注意产品文案更改，只支持单选了** |
| taskSource | String | 任务来源筛选<br>ALL：全部任务<br>RECOMMEND：推荐任务 |

*   出参
    

```json
{
    "result": true,
    "code": "00000",
    "message": null,
    "version": "1.0",
    "timestamp": "1753099894907",
    "success": true,
    "msgInfo": "调用成功",
    "msgCode": "SUCCESS",
    "traceId": "0bfb408b17530998928682094e162f",
    "data": {
        "result": true,
        "traceId": "0bfb408b17530998928682094e162f",
        "code": "1",
        "data": {
                "dataList": [
                    {
                        "recommendType": "",     // SYSTEM/REMIND/ARRANGE  对应 系统推荐/主管催办/主管下发
                        "taskName": "",
                        "taskType": "",
                        "targetStatus": "",     // 未完成：INCOMPLETE 已完成：COMPLETED 
                        "shopCount": "", 
                        "suggestCommunication":""     // 最高优任务推荐话术
                        "completeValue": ""   // "完成后的价值"
                        "diagnoseAdvice":""      // 任务诊断建议
                        "viewShopList":""  // true 代表有门店列表，false代表没有门店列表
                        "targetId":"" // 任务对应的pid
                        "targetType":""  // 固定值为：VisitTargetTypeEnum.MERCHANT
                        "bizScene":"" // 固定值为：ToolkitBaseBizSceneEnum.C33_AGENT_OUT_CALL
                        "actions": [
                        {
                            "buttonName": "去完成",
                            "greyButton": false,
                            "showButton": true,
                            "buttonCode": "TO_COMPLETE"
                            "url":""
                        },
                        {
                            "buttonName": "提交结果",
                            "greyButton": false,
                            "showButton": true,
                            "buttonCode": "SUBMIT_RESULT"
                            "url":
                        }
                    ],
                ]
            }
        },
        "success": true,
        "message": null,
        "msgInfo": "调用成功",
        "version": "1.0",
        "msgCode": "SUCCESS",
        "timestamp": "1753099894900"
    }
}

```

*********.4 查询商户任务详情内嵌的门店列表**

**产品形态：**

![image.png](/core/api/resources/img/5eecdaf48460cde5f89de59e9fed085b6957f7519871d6e075b8339e1c4c24833b9b4bbc6ab87983fb53e1e6afc12419a156a98577f418d5754e1022a93861c9dd487db0192b33677ec96a88ab7ff40a140dcd80cb7a9a2b636453bc1f727f91)

接口 amap-sales-operation.AgentOperationQueryFacade.queryMerchantTaskShopList

*   入参 ：MerchantTaskShopListRequest请求参数 
    

| 参数名 | 参数类型 | 参数示例说明 |
| --- | --- | --- |
| merchantId | String |  |
| taskType | String | 上一个接口queryMerchantTaskDetail会把任务类型带出来 |
| taskStatus | String | 待处理：PROCESSING<br>未完成：INCOMPLETE  <br>已完成：COMPLETED<br>**注意产品文案更改，只支持单选了** |
| **分页查询参数（页码，每页几个）** |  |  |

*   出参
    

```json
{
   "success": true,
   "errorCode": "SUCCESS",
   "errorMsg": "操作成功",
   "model": {
   "dataList": [
                {
                  "shopName": "",
                  "recommendShopAnalysis": ""
                },
                {
                  "shopName": "",
                  "recommendShopAnalysis": ""
                } 
             ],
       "pageInfo": {
       "hasMore": false,
       "totalPage": 1,
       "currentPageNo": 1,
       "nextPageNo": 2,
       "pageSize": 10,
       "totalCount": 3
     }
   }
}
```

##### 4.1.3.7 门店列表  $\color{#0089FF}{@孟昊(沐寒)}$ 

*   线上saro：[http://saro.alibaba-inc.com/eflow/editor/e-50es433as411/edit](http://saro.alibaba-inc.com/eflow/editor/e-50es433as411/edit)
    
*   预发saro：[http://saro.alibaba-inc.com/eflow/editor/e-da370o2279n3/edit](http://saro.alibaba-inc.com/eflow/editor/e-da370o2279n3/edit)
    

[请至钉钉文档查看「白板」](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mf9gxfe8qfuwafln7gf&rnd=0.0019112956181871477)

### 4.2 改动的内容

【文字也需要详细说明】

### 4.3 本系统各改动内容中的重要关注点。

### 4.4 外围系统设计的关键改动点。

【供测试链路串联】

## 5. 影响面分析

### 5.1 已有代码逻辑的影响

### 5.2 已有核对脚本的影响

### 5.3 已有监控的影响

### 5.4 已有数据的影响

【数据兼容性】

### 5.5 已有jar的影响

【jar包和接口兼容性】

## 6. 接口定义

### 灰度开关

接口：amap-sales-operation.OptConfigQueryHsf.businessSceneSwitch

入参：

```json
{
  "sceneList" : [
    "TASK_PRIORITY_V2" // 本次新增
  ]
}
```

出参：

```json
{
    "code": "1",
    "data": [
      {
        "scene" : "TASK_PRIORITY_V2",
        "switchStatus" : true // true=命中灰度
      } 
    ],
    "msgCode": "SUCCESS",
    "msgInfo": "调用成功",
    "result": true,
    "success": true
}
```

### 查询绩效数据  $\color{#0089FF}{@余浩(萧启)}$ 

**接口**：amap-sales-operation.FireMapsFacade.queryPerformanceTargets

**入参**：

```json
[
  {
    "commonOperatorInfo": {
      "amapUid": "",
      "sellerId": "",
      "operatorNickName": "",
      "operatorType": "",
      "partnerId": "",
      "comId": "",
      "source": "",
      "class": "com.amap.sales.operation.client.common.CommonOperatorInfo",
      "operatorId": "",
      "operatorName": "",
      "workId": ""
    },
    "jobLevel": 1,
    "entityType": "JOB",
    "channelOptStaffIds": [],
    "channelCompanyIds": [],
    "queryDate": "20250922",
    "staffIds": [],
    "gatewaySource": "",
    "requestId": "",
    "pageSource": "MANAGER_PAGE",
    "jobIds": [
      "1"
    ],
    "page": {
      "pageNo": 0,
      "pageSize": 0,
      "class": "com.amap.sales.operation.client.common.page.PageRequest$Page"
    },
    "class": "com.amap.sales.operation.client.request.firemaps.PerformanceTargetRequest"
  }
]
```

**出参**：

```json
{
  "result": true,
  "traceId": "212aa24c17586313843913947ebd94",
  "code": "1",
  "data": {
    "queryDate": "20250922",
    "dataList": [
      {
        "unit": "元",
        "indicatorName": "总收入",
        "targetCompletionRate": "0.553872860108",
        "indicatorCode": "TOTAL_INCOME",
        "targetValue": "*********.510650",
        "indicatorValue": "*********.477897",
        "class": "com.amap.sales.operation.client.dto.firemaps.PerformanceIndicatorDTO",
        "monthOnMonthRate": null,
        "weekOnWeekRate": null
      },
      {
        "unit": "万",
        "indicatorName": "广告收入",
        "targetCompletionRate": null,
        "indicatorCode": "AD_REVENUE",
        "targetValue": null,
        "indicatorValue": "301862711.957897",
        "class": "com.amap.sales.operation.client.dto.firemaps.PerformanceIndicatorDTO",
        "monthOnMonthRate": null,
        "weekOnWeekRate": null
      },
      {
        "unit": "万",
        "indicatorName": "年费收入",
        "targetCompletionRate": null,
        "indicatorCode": "ANNUAL_FEE_REVENUE",
        "targetValue": null,
        "indicatorValue": "34066388.840000",
        "class": "com.amap.sales.operation.client.dto.firemaps.PerformanceIndicatorDTO",
        "monthOnMonthRate": null,
        "weekOnWeekRate": null
      },
      {
        "unit": "万",
        "indicatorName": "CPS收入",
        "targetCompletionRate": null,
        "indicatorCode": "CPS_REVENUE",
        "targetValue": null,
        "indicatorValue": "3544194.680000",
        "class": "com.amap.sales.operation.client.dto.firemaps.PerformanceIndicatorDTO",
        "monthOnMonthRate": null,
        "weekOnWeekRate": null
      },
      {
        "unit": "万",
        "indicatorName": "智能体收入",
        "targetCompletionRate": null,
        "indicatorCode": "INTELLIGENT_BODY_REVENUE",
        "targetValue": null,
        "indicatorValue": "134500.000000",
        "class": "com.amap.sales.operation.client.dto.firemaps.PerformanceIndicatorDTO",
        "monthOnMonthRate": null,
        "weekOnWeekRate": null
      },
      {
        "unit": "%",
        "indicatorName": "首充率",
        "targetCompletionRate": null,
        "indicatorCode": "FIRST_CHARGE_RATE",
        "targetValue": null,
        "indicatorValue": "0.242760680431",
        "class": "com.amap.sales.operation.client.dto.firemaps.PerformanceIndicatorDTO",
        "monthOnMonthRate": null,
        "weekOnWeekRate": null
      },
      {
        "unit": "%",
        "indicatorName": "留存率",
        "targetCompletionRate": null,
        "indicatorCode": "RETENTION_RATE",
        "targetValue": null,
        "indicatorValue": "0.466438796323",
        "class": "com.amap.sales.operation.client.dto.firemaps.PerformanceIndicatorDTO",
        "monthOnMonthRate": null,
        "weekOnWeekRate": null
      }
    ],
    "pageInfo": null,
    "class": "com.amap.sales.operation.client.dto.firemaps.PerformanceTargetDTO"
  },
  "success": true,
  "message": null,
  "class": "com.autonavi.openplatform.commons.result.ResultDTO",
  "msgInfo": "调用成功",
  "version": "1.0",
  "msgCode": "SUCCESS",
  "timestamp": "1758631385227"
}
```

### 查询指标完成度（柱状图）

**接口**：amap-sales-operation.FireMapsFacade.queryPerformanceDataBar

**入参**：

```json
[
  {
    "commonOperatorInfo": {
      "amapUid": "",
      "sellerId": "",
      "operatorNickName": "",
      "operatorType": "",
      "partnerId": "",
      "comId": "",
      "source": "",
      "class": "com.amap.sales.operation.client.common.CommonOperatorInfo",
      "operatorId": "",
      "operatorName": "",
      "workId": ""
    },
    "selectedIndicator":"TOTAL_INCOME",
    "jobLevel": 1,
    "entityType": "JOB",
    "channelOptStaffIds": [
     
    ],
    "channelCompanyIds": [
     
    ],
    "queryDate": "20250922",
    "staffIds": [
     
    ],
    "pageSource": "MANAGER_PAGE",
    "jobIds": [
      "1"
    ],
   
    "class": "com.amap.sales.operation.client.request.firemaps.PerformanceDataBarRequest"
  }
]
```

**出参**：

```json
{
  "result": true,
  "traceId": "212cdf1b17586315310956762ee4c5",
  "code": "1",
  "data": {
    "queryDate": "20250922",
    "dataList": [
      {
        "indicatorName": "总收入",
        "entityType": "JOB",
        "entityName": "高德销服科技",
        "targetCompletionRate": "0.553872860108",
        "indicatorCode": "TOTAL_INCOME",
        "entityId": "1",
        "class": "com.amap.sales.operation.client.dto.firemaps.BarChartDataDTO",
        "currentValue": "*********.477897",
        "monthOnMonthRate": null,
        "weekOnWeekRate": null
      }
    ],
    "pageInfo": {
      "totalPage": 1,
      "hasMore": false,
      "pageSize": 10,
      "nextPageNo": 2,
      "totalCount": 1,
      "class": "com.amap.sales.operation.client.common.PageInfoDTO",
      "currentPageNo": 1
    },
    "class": "com.amap.sales.operation.client.dto.firemaps.PerformanceDataBarDTO",
    "disclaimer": "绩效数据最终以绩效组下发为准，此数据仅供参考"
  },
  "success": true,
  "message": null,
  "class": "com.autonavi.openplatform.commons.result.ResultDTO",
  "msgInfo": "调用成功",
  "version": "1.0",
  "msgCode": "SUCCESS",
  "timestamp": "1758631531496"
}
```

### 查询策略建议

**接口**：amap-sales-operation.OptMatterQueryFacade.generateOperationDiagnosisSuggestion

**入参**：

```json
{
    "operatorId": "OP001",
    "operatorName": "张三",
    "queryDate": "20240115",
    "commonOperatorInfo": {
        "operatorId": "OP001",
        "operatorName": "张三"
    }
}
```

**出参**：

```json
{
  "result": true,
  "traceId": "212c9ae717586323511525689eec41",
  "code": "1",
  "data": {
    "generationTime": null,
    "diagnosisSuggestion": "今日推荐，共13个商家（",
    "class": "com.amap.sales.operation.client.dto.agentoperation.OperationDiagnosisDTO"
  },
  "success": true,
  "message": null,
  "class": "com.autonavi.openplatform.commons.result.ResultDTO",
  "msgInfo": "调用成功",
  "version": "1.0",
  "msgCode": "SUCCESS",
  "timestamp": "1758632351820"
}
```

### 查询商家任务饼状图

**接口**：amap-sales-operation.OptMatterQueryFacade.queryTodayMerchantTodoTaskPieChart

**入参**：

```json
{
    "operatorId": "OP001",
    "operatorName": "张三",
    "queryDate": "20240115",
    "commonOperatorInfo": {
        "operatorId": "OP001",
        "operatorName": "张三"
    }
}
```

**出参**：

```json
{
  "result": true,
  "traceId": "2136014817586326408278134e188e",
  "code": "1",
  "data": {
    "pieChartData": [],
    "strategySuggestion": "今日有13个商家需完成效果优化任务，快去完成吧",
    "class": "com.amap.sales.operation.client.dto.agentoperation.MerchantTodoTaskPieChartDTO"
  },
  "success": true,
  "message": null,
  "class": "com.autonavi.openplatform.commons.result.ResultDTO",
  "msgInfo": "调用成功",
  "version": "1.0",
  "msgCode": "SUCCESS",
  "timestamp": "1758632641551"
}
```

### 查询商户列表  $\color{#0089FF}{@余浩(萧启)}$ 

```json
{
  "recommendTaskSources": ["SYSTEM", "MANUAL"],
  "merchantStages": ["NEWBIE", "GROWING", "MATURE", "BOTTLENECK", "COMPLETED"],
  "todoTaskTypes": ["STORE_DECORATION", "SHELF_HAS_PRODUCTS", "ADVERTISING_TASK", "INFRASTRUCTURE_TASK"],
  "adTaskLabels": ["BALANCE_WARNING", "STOP_EXPOSURE_RECALL", "NEW_SIGN", "NO_BALANCE_CONTINUE"],
  "warningTaskLabels": ["LOW_SATISFACTION", "HIGH_RISK"],
  "viewOperatorId": "123456789",
  "filterOptRelation": false,
  "highPotentialValues": ["1", "2"]
}
```

### 查询门店列表  $\color{#0089FF}{@孟昊(沐寒)}$ 

接口：amap-sales-operation.AgentOperationQueryFacade.queryAgentOperationShopList

*   入参 ：TaskUrgeRequest 请求参数 
    

| 参数名 | 参数类型 | 参数示例说明 |
| --- | --- | --- |
| **\[****新增****\]** <br>shopScoreLevels | String数组 | 枚举：<br>LV1<br>LV2<br>LV3<br>LV4<br>LV5 |
| **\[****新增****\]** <br>shopOperateStage | String | 枚举：<br>NEWBIE：新手期<br>BOTTLENECK：瓶颈期<br>DEVELOP：成长期<br>MATURE：成熟期 |

*   出参
    

```json
{
    "result": true,
    "code": "00000",
    "message": null,
    "version": "1.0",
    "timestamp": "1753099894907",
    "success": true,
    "msgInfo": "调用成功",
    "msgCode": "SUCCESS",
    "traceId": "0bfb408b17530998928682094e162f",
    "data": {
        "result": true,
        "traceId": "0bfb408b17530998928682094e162f",
        "code": "1",
        "data": {
            "dataList": [
                {
                    "shopName": "文明村烧烤吧",
                    "pid": "1088013502221685",
                    "shopId": "2016101900077000000019261428|xiniu_xgc_bgc",
                    ..... // 省略其他字段
                    "shopScoreLevel": "LV3", // 新增，商家分等级
                    "shopOperateStage" : "成长期" // 新增，运维阶段
                }
            ],
            "pageInfo": {
                "totalPage": 15,
                "hasMore": true,
                "pageSize": 10,
                "nextPageNo": 2,
                "totalCount": 143,
                "currentPageNo": 1
            }
        },
        "success": true,
        "message": null,
        "msgInfo": "调用成功",
        "version": "1.0",
        "msgCode": "SUCCESS",
        "timestamp": "1753099894900"
    }
}
```

### 任务类型查询接口  $\color{#0089FF}{@孟昊(沐寒)(沐寒)}$ 

接口：amap-sales-operation.OptConfigQueryHsf.queryPriorityTaskType

入参：

```json
"LEADER_URGES" // 主管催办
或 
"LEADER_ISSUE" // 主管下达
```

出参：

```json
{
    "code": "1",
    "data": {
      "typeList" : [
        {
          "taskType" : "XXXX", // 任务code
          "taskName" : "货架有品" // 任务名称
        } 
      ]
    },
    "msgCode": "SUCCESS",
    "msgInfo": "调用成功",
    "result": true,
    "success": true
}
```

### 基建任务详情

接口：amap-sales-operation.AgentOperationQueryFacade.queryAgentOperationDetail

入参：无变更

出参：

```json
{
    "code": "1",
    "data": {
        "taskDetailDTOList" : [],
        "shopId" : "",
        "shopScoreLevel" : "LV3" // 商家分等级
     },
    "msgCode": "SUCCESS",
    "msgInfo": "调用成功",
    "result": true,
    "success": true
}
```

## 6. 预案评估

*   switch：
    
*   限流：
    
*   降级：
    

## 7. 核对评估

## 8. 监控评估

*   任务下发堆积
    
*   作战地图查询性能
    
*   任务各项查询性能
    
*   **FBI报表：使用率、完成率、过程指标**
    

## 9. 压测评估

无

## 10. 灰度评估

*   灰度开关：**小二维度灰度**，taskPriorityV2GreyModel
    
    *   **默认双写**，仅灰度控制切读流量
        

[请至钉钉文档查看「白板」](https://alidocs.dingtalk.com/i/nodes/DnRL6jAJM7xP3M1otKjOXwryWyMoPYe1?doc_type=wiki_doc&iframeQuery=anchorId%3DX02mfovacohwg94sgthzk&rnd=0.0019112956181871477)

### 新旧表迁移方案：

纯新增场景：门店装修，货架有品，商品分达标，商家等级，五星货架，电话实名制 ，主管下发（广告任务、基建任务、团购任务、大促任务、场域招商、签续任务、其他任务）

待更新场景：新签上线沟通，首续复盘任务，续充复盘任务，门店评价客诉，风控任务

历史数据：报表

#### 阶段一，上线前：

*   新表创建
    
*   代码支持新旧表双写
    
    *   纯新增任务的写操作，仅写入新表
        
    *   待更新任务的写操作（INSERT/UPDATE）改为**双写**：先写旧表，成功后再异步写新表；
        
        *   新增开关：是否写新旧双写；此时为：双写
            
        *   记录写新表失败日志，记录trace\_id在commom\_task表，方便回溯
            
        *   目前待更新数据量：161664      （9.9数据)
            
    *   ![image.png](/core/api/resources/img/5eecdaf48460cde516c10b2dff77572cea921d8321ab99d075b8339e1c4c24831b75b38faadcd24bec177c308ebd5304762ae4ce84f8d8d56f0912ed9fb813b21a0bb1d1ec2631002c948eb479dc190310a08224093f01f64fb4c8ed7016461c)
        
        *   迁移方案：针对高频任务预迁移 ：任务创建时间为上线前7天内，提前迁移至新表
            
    *                            低频数据：当任务收到更新操作时，先更新旧表状态，再异步将旧表数据迁移至新表
        
*   新表具备写入能力，但仍读旧表
    
    *   历史读接口新增开关，读旧表
        
    *   开关需要支持按pid，saleId ,任务类型 灰度验证
        

#### 阶段二，上线后，双写验证中：

*   新旧表数据一致性校验 
    
    *   定期脚本：对比新增数据，待更新任务数据
        
*   异常case修复，失败日志校验
    
*   数据观测2-3天
    
*   仍读旧表
    

#### 阶段三，上线后，灰度切换：

*   按照saleId，任务类型 小流量灰度
    
    *   先切纯新增任务的读：此类数据仅存在于新表中
        
        *   任务类型：门店装修，货架有品，商品分达标，商家等级，五星货架，电话实名制 ，主管下发（广告任务、基建任务、团购任务、大促任务、场域招商、签续任务、其他任务）
            
    *   再切待更新任务的读：此时7天内的数据以及上线后有写操作的任务，存在于新旧两张表中
        
        *   任务类型：新签上线沟通，首续复盘任务，续充复盘任务，门店评价客诉，风控任务
            
    *   最后切纯历史数据的读：报表？报表需要同时结合新旧两张表出
        
*   旧表涉及的调度操作：如通知等，按照pid，saleId，任务类型 小流量灰度
    
    *   agent\_task 涉及到差评推送
        
    *   task\_instance 
        

#### 阶段四，完全切换：    

*   切换开关：是否写新旧双写；此时为：仅写新表
    
*   灰度切换 -> 全量读新表
    
*   后续代码删除写旧表的操作
    

## 11. 应急评估

## 12. 重点关注点

## 13. 外围系统容量评估

## 14. 发布依赖评估

## 15. 测试回归建议

## 16. 发布回滚评估

## 17.人日&排期

### 17.1 人日

*   亦竟：9 + 2 = 11
    
*   萧启：6 + 2 + 2 = 10
    
*   烦无：7 + 3 = 10
    
*   橙希：4 + 5 = 9
    
*   榆澄：3 + 3 + 3 = 9
    
*   沐寒：2 + 0.5 + 0.5  + 2 + 2 = 7
    

| **工作内容** | **分工** | **人日** |
| --- | --- | --- |
| 灰度方案 | 沐寒 | 代运营：2 |
| 门店分层规则<br>*   阶段更新（ODPS->DB） | 沐寒 | 数据：\*<br>代运营：0.5 |
| 作战地图-团队筛选<br>*   支持直营/渠道选部门、人<br>    <br>*   服务商配置渠道运维 |  | 销售基础<br> $\color{#0089FF}{@张洋瑜(不胜)}$ |
| 作战地图-身份mock | 沐寒 | 代运营：0.5 |
| 作战地图-绩效目标（主管视角/小二视角）<br>*   总收入：当前值+目标（依赖政策）1<br>    <br>*   其他7项指标：取数仓原子指标+环比计算 2<br>    <br>*   柱状图：团队聚合 3 | 萧启 | 数据  $\color{#0089FF}{@潘戈强(晏晨)}$ <br>绩效  $\color{#0089FF}{@苏引}$ <br>代运营：6 |
| 作战地图-门店仪表盘（主管视角/小二视角）<br>*   门店阶段分布，按所选部门层级进行数据聚合（日/周/月环比）2.5<br>    <br>*   诊断建议（各阶段门店占比环比上周，各阶段下降幅度top3团队/小二）2.5<br>    <br>    *   查询复用：诊断-小二/服务商诊断 1.5<br>        <br>    *   小二工作台：概览-门店分布 0.5<br>        <br>*   各阶段门店分布饼图：2<br>    <br>    *   新手：时长指标<br>        <br>    *   瓶颈/成长：凭证指标<br>        <br>    *   成熟：arpu指标 | 亦竟 | 数据：\*<br>代运营：9 |
| 作战地图-分阶段诊断<br>*   新手：任务 + >x天门店占比 + 组织柱状图 1<br>    <br>    *   诊断建议（超时数量top3团队/小二）1<br>        <br>*   瓶颈/成长：红黄绿（凭证数）门店数堆积柱状图 2<br>    <br>    *   诊断建议（绿色数量top3团队/小二）1<br>        <br>*   成熟：红黄绿（ARPU）门店数堆积柱状图 1<br>    <br>    *   诊断建议（绿色数量top3团队/小二）1 | 烦无 | 数据：\*<br>代运营：7 |
| 作战地图-任务汇总/明细 | 烦无 | 数据：\*<br>代运营：3 |
| 工作台-工作概览<br>*   门店分布<br>    <br>*   诊断建议 1<br>    <br>*   钉钉预警 1 | 萧启 | 代运营：2 |
| 任务定义（确定性/时效性）<br>*   门店维度任务<br>    <br>*   商户维度任务 | 亦竟 | 代运营：2 |
| 任务下发<br>*   系统下发 2<br>    <br>*   excel导入下发任务 2<br>    <br>*   excel导入记录 1 | 橙希 | 数据：\*<br>代运营：5 |
| 任务执行<br>*   货架/LV登记/基础分<br>    <br>*   拜访记录<br>    <br>*   填写表单 | 橙希 | 数据：\*<br>代运营：4 |
| 任务过期<br>*   执行过期<br>    <br>*   推荐过期 | 沐寒 | 代运营：2 |
| 主管催办<br>*   催办列表任务查询 1 <br>    <br>*   催办tab页上方，诊断信息查询 1<br>    <br>*   任务催办提交（打标）1<br>    <br>*   催办任务提醒 0.5 <br>    <br>*   候选任务列表 0.5  $\color{#0089FF}{@孟昊(沐寒)}$ | 榆澄 | 代运营：3.5 |
| 任务推荐<br>*   系统推荐日滚（ODPS清洗打标->MQ）2<br>    <br>*   首页-腰部推荐（今日沟通）1 | 榆澄 | 代运营：3 |
| 商户列表<br>*   saro+Ha3：任务推荐来源、门店分层、无群商户 | 萧启 | 代运营：2 |
| 门店列表<br>*   saro+Ha3：商家分Lv标、门店阶段<br>    <br>*   列表，展示拒下等级xx分（商家侧后续提供该能力，本期暂不支持） | 沐寒 | 代运营：2 |
| 商户详情<br>*   商户引擎saro+Ha3改造 1<br>    <br>*   推荐任务tab 1<br>    <br>*   全部任务tab 1 | 榆澄 | 代运营：3 |
| AB试验能力 | 沐寒 | 代运营：2 |
|  |  |  |
| 总计 |  | 55.5pt |

### 17.2 排期

*   联调：9,23上班开始联调
    
*   提测：9.25下班前提测
    
*   发布：10.16发布
    
*   灰度：
    

# 附录

**系分评审-会议纪要**

*   [x] 1.差评企微推送，命中灰度查询切到新表 $\color{#0089FF}{@白琛曦(橙希)}$ 
    
*   [x] 2.task\_instance\_v2表，新增out\_biz\_id，关联差评id $\color{#0089FF}{@白琛曦(橙希)}$ 
    
*   [x] 3.task\_instance\_v2两个状态status、targetStatus，需要同步更新  $\color{#0089FF}{@白琛曦(橙希)}$ 
    
*   [x] 4.续签任务下发，临期60天到期，时序图下发条件=60天，改为<=60天  $\color{#0089FF}{@白琛曦(橙希)}$ 
    
*   [x] 5.excel下发任务，pid合法性校验  $\color{#0089FF}{@白琛曦(橙希)}$ 
    
*   [x] 6.com.amap.sales.operation.client.OptConfigQueryHsf，补两个接口，用于返回任务类型。分别用于主管催办页面，主管下达页面  $\color{#0089FF}{@白琛曦(橙希)}$  $\color{#0089FF}{@唐皓源(榆澄)}$ 
    
*   [x] 7.绩效时序图，把无关的内容去掉  $\color{#0089FF}{@余浩(萧启)}$ 
    
*   [x] 8.工作概览返回文本，支持markdown语法  $\color{#0089FF}{@余浩(萧启)}$ 
    
*   [ ] 9.门店仪表盘缓存key和萧启方案保持一致  $\color{#0089FF}{@徐文洋(亦竟)}$ 
    
*   [ ] 10.雾化数据湖方案，成本成本  $\color{#0089FF}{@徐文洋(亦竟)}$ 
    
*   [ ] 11.跟算法的输入、输出表字段，以及算法依赖的数据，沟通一下  $\color{#0089FF}{@唐皓源(榆澄)}$  $\color{#0089FF}{@孟昊(沐寒)}$ 
    

**系分评审-待讨论点**

*   [x] 1.差评任务，原需求：仅商家分层SKA/KA下发，~~新需求：识别小二组织~~  $\color{#0089FF}{@白琛曦(橙希)}$  $\color{#0089FF}{@孟昊(沐寒)}$  $\color{#0089FF}{@翟震坤(镇坤)}$ 
    
*   [x] 2.自定义任务，统一切拜访  $\color{#0089FF}{@白琛曦(橙希)}$  $\color{#0089FF}{@孟昊(沐寒)}$  $\color{#0089FF}{@翟震坤(镇坤)}$ 
    
    需要给拜访提需  $\color{#0089FF}{@翟震坤(镇坤)}$ 
    
*   [ ] 3.ha3人户引擎，新增推荐任务id列表字段  $\color{#0089FF}{@唐皓源(榆澄)}$  $\color{#0089FF}{@孟昊(沐寒)}$ 
    
*   [ ] 4.查询部门下的人员  $\color{#0089FF}{@金梦瑶(梦瑶)}$  $\color{#0089FF}{@张洋瑜(不胜)}$  $\color{#0089FF}{@孟昊(沐寒)}$ 
    
*   [ ] 5.部门白名单逻辑  $\color{#0089FF}{@金梦瑶(梦瑶)}$ 
    
*   [x] 6.服务商维度催办，不校验负载任务数量  $\color{#0089FF}{@吴福旺(烦无)}$  $\color{#0089FF}{@翟震坤(镇坤)}$ 
    
*   [x] 7.商户分层标签----聚合门店  $\color{#0089FF}{@余浩(萧启)}$