{"private": true, "name": "xy-task-pc", "version": "1.54.0", "description": "任务管理", "author": "fengsi.fs", "scripts": {"dev": "ice start --no-open", "start": "ice start --no-open -h pre-local-xy.amap.com --https self-signed", "clean": "<PERSON><PERSON><PERSON> dist", "build": "ice build && node ./afterBuild.js", "alsclint-scan": "alsclint scan -i ./src", "alsclint-fix": "alsclint fix"}, "dependencies": {"@ali/amap-react-studio": "^0.1.7", "@ali/ice-plugin-qiankun": "^1.2.2", "@ali/kb-fetch": "^2.2.2", "@alife/afe-clipboard": "^1.1.0", "@alife/alsc-fetch-types": "^1.0.9", "@alife/amap-aes-trace": "^2.1.0", "@alife/amap-fetch": "^3.3.3", "@alife/amap-kpi-shared-components": "^2.0.3", "@alife/amap-mp-utils": "^2.3.3", "@alife/amap-tracker": "^0.1.10", "@alife/echo-client": "1.0.23", "@alife/growth-center-usecase": "0.1.0-beta.5", "@alife/kb-biz-util": "^1.4.4", "@alife/kb-m-biz-util": "^2.3.4", "@alife/mo-bd-select": "^2.4.3", "@alife/mo-common-category-select": "^2.4.4", "@alife/mo-image-viewer": "^2.3.1", "@alife/mo-job-tree-select": "^2.6.4-beta.2", "@alife/mo-koubei-agent": "^2.5.4", "@alife/mo-select-shop-m": "^2.4.2", "@alife/mo-select-shop-pc-v4": "^2.5.3", "@alife/mp-dev-shared-utils": "^1.0.7", "@alife/mp-oss-upload": "^2.6.8", "@alife/mp-task-item": "^1.0.2", "@alife/peng-yi-peng": "1.0.1", "@amap/amap-jsapi-loader": "^1.0.1", "@amap/amap-react": "^0.1.5", "@ant-design/icons": "^4.8.3", "@ant-design/plots": "^2.6.3", "@ant-design/pro-components": "^2.8.10", "@dhdbstjr98/gif.js": "^1.0.2", "@ice/runtime": "^1.4.11", "@uiw/react-markdown-preview": "^5.1.5", "ahooks": "^3.9.0", "ali-oss": "^6.23.0", "antd": "^5.24.3", "bizcharts": "^4.1.23", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.11", "echarts": "^5.6.0", "eventemitter3": "^5.0.1", "html2canvas": "^1.4.1", "number-precision": "^1.6.0", "numeral": "^2.0.6", "query-string": "^7.1.3", "radash": "^12.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-error-boundary": "^4.0.12", "react-quill": "^2.0.0", "react-router-dom": "^6.26.1", "react-sticky-box": "^2.0.5", "styled-components": "^6.1.11", "tslib": "2.6.3", "umi-request": "^1.4.0", "xlsx": "^0.18.5"}, "devDependencies": {"@alife/alsclint": "^1.1.0", "@alife/ice-plugin-mp-tracker": "^0.1.2", "@ice/app": "^3.4.11", "@types/numeral": "^2.0.5", "lint-staged": "^10.5.4", "proxy-agent": "^6.4.0", "rimraf": "2", "typescript": "^4.7.4"}, "publishConfig": {"registry": "**************************:alsc-merchants/xy-task-pc.git"}, "tnpm": {"mode": "npm", "lockfile": "enable"}, "pre-commit": ["precommit"], "repository": "**************************:alsc-merchants/xy-task-pc.git"}